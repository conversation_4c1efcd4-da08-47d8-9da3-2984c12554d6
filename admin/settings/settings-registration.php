<?php
// admin/settings/settings-registration.php

require_once plugin_dir_path(__FILE__) . '/settings-options.php';

function vecura_register_settings() {
    /**
     * global 
     */ 
    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_text_color', array(
        'type' => 'string',
        'default' => '#000000',
        'sanitize_callback' => 'sanitize_text_field'
    ));

    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_accent_color_primary', array(
        'type' => 'string',
        'default' => '#17436e',
        'sanitize_callback' => 'sanitize_text_field'
    ));

    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_accent_color_secondary', array(
        'type' => 'string',
        'default' => '#17436e',
        'sanitize_callback' => 'sanitize_text_field'
    ));
    
    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_accent_color_tertiary', array(
        'type' => 'string',
        'default' => '#17436e',
        'sanitize_callback' => 'sanitize_text_field'
    ));

    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_background_color', array(
        'type' => 'string',
        'default' => '#17436e',
        'sanitize_callback' => 'sanitize_text_field'
    ));

    /* Channels */
    // Youtube API 
    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_youtube_api_key', array(
        'type' => 'string',
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field'
    ));
    // Neue Einstellung für die Channel IDs (als kommaseparierten String)
    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_youtube_channels', array(
        'type' => 'string',
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field'
    ));

    /**
     * social wall  
     */ 
    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_social_wall_hide_categories', array(
        'type' => 'boolean',
        'default' => false,
        'sanitize_callback' => 'rest_sanitize_boolean'
    ));
    
    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_social_wall_hide_date', array(
        'type' => 'boolean',
        'default' => false,
        'sanitize_callback' => 'rest_sanitize_boolean'
    ));
    
    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_social_wall_category_style', array(
        'type' => 'string',
        'default' => 'default',
        'sanitize_callback' => function($input) {
        // Hole alle erlaubten Werte aus den Options
            $allowed_values = array_map(function($item) {
                return $item['value'];
            }, get_category_styles());
            
            return in_array($input, $allowed_values) ? $input : 'default';
        }
    ));

    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_social_wall_cta_text', array(
        'type' => 'string',
        'default' => 'Read more',
        'sanitize_callback' => 'sanitize_text_field'
    )); 

    /**
     * News 
     */
    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_news_source', array(
        'type' => 'string',
        'default' => 'cpt',
        'sanitize_callback' => function($input) {
            // Erlaubte Werte: 'cpt' (eigene News) oder 'posts' (Blogbeiträge)
            $allowed_values = ['cpt', 'posts'];
            return in_array($input, $allowed_values) ? $input : 'cpt';
        }
    ));


    /**
     * Videos
     */
    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_videothek_slider_aspect_ratio_width', array(
        'type' => 'string',
        'default' => '3',
        'sanitize_callback' => 'sanitize_text_field'
    )); 

    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_videothek_slider_aspect_ratio_height', array(
        'type' => 'string',
        'default' => '4',
        'sanitize_callback' => 'sanitize_text_field'
    )); 
    
     register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_videothek_hero_aspect_ratio_width', array(
        'type' => 'string',
        'default' => '16',
        'sanitize_callback' => 'sanitize_text_field'
    )); 

    register_setting('vecura_mediathek_settings_group', 'vecura_mediathek_videothek_hero_aspect_ratio_height', array(
        'type' => 'string',
        'default' => '9',
        'sanitize_callback' => 'sanitize_text_field'
    )); 

    
    // add_settings_section(
    //     'vecura_mediathek_settings_section',
    //     __('Einstellungen', 'textdomain'),
    //     null,
    //     'vecura-mediahub-settings'
    // );
}
add_action('admin_init', 'vecura_register_settings');