<?php
// admin/settings/settings-page.php

require_once plugin_dir_path(__FILE__) . '/settings-options.php';

/** 
 * Save Settings function for AJAX Handler
 */
function vecura_save_settings() {
    $debug = array(
        'step1_post' => $_POST,
        'step2_settings' => null,
        'step3_updates' => array()
    );

    if (!check_ajax_referer('vecura_mediathek_nonce', 'nonce', false) || !current_user_can('manage_options')) {
        wp_send_json_error($debug);
        return;
    }

    $settings = json_decode(stripslashes($_POST['settings']), true);
    $debug['step2_settings'] = $settings;
    
    foreach ($settings as $key => $value) {
        $option_name = 'vecura_mediathek_' . $key;
        $before_value = get_option($option_name);

         // Falls es sich um die YouTube Channels handelt und der Wert ein Array ist, in JSON umwandeln.
        if ($key === 'youtube_channels' && is_array($value)) {
            $value = json_encode($value);
        }
        
        // Prüfe ob die Option registriert ist
        $registered_settings = get_registered_settings();
        $is_registered = isset($registered_settings[$option_name]);
        
        $updated = update_option($option_name, $value);
        $after_value = get_option($option_name);
        
        $debug['step3_updates'][$option_name] = array(
            'is_registered' => $is_registered,
            'value_before' => $before_value,
            'tried_to_save' => $value,
            'update_success' => $updated,
            'value_after' => $after_value
        );
    }

    wp_send_json_success($debug);
}
add_action('wp_ajax_vecura_save_settings', 'vecura_save_settings');

function vecura_render_settings_page() {

    // Falls noch nichts gespeichert wurde, wird ein leerer String geliefert.
    $youtube_channels_option = get_option('vecura_mediathek_youtube_channels', '');

    // Versuche, den JSON-String in ein Array zu decodieren. Wenn das fehlschlägt, nutze ein leeres Array.
    $youtube_channels = !empty($youtube_channels_option) ? json_decode($youtube_channels_option, true) : array();
    if ( ! is_array($youtube_channels) ) {
        $youtube_channels = array();
    }

    // Get Data from WP Databse
    $settings = array(
        // global 
        'text_color' => get_option('vecura_mediathek_text_color', '#000000'),
        'accent_color_primary' => get_option('vecura_mediathek_accent_color_primary', '#338feb'),
        'accent_color_secondary' => get_option('vecura_mediathek_accent_color_secondary', '#64c8ff'),
        'accent_color_tertiary' => get_option('vecura_mediathek_accent_color_tertiary', '#17436e'),
        'background_color' => get_option('vecura_mediathek_background_color', '#17436e'),

        // soacial wall
        'social_wall_hide_categories' => get_option('vecura_mediathek_social_wall_hide_categories', false),
        'social_wall_hide_date' => get_option('vecura_mediathek_social_wall_hide_date', false),
        'social_wall_category_style' => get_option('vecura_mediathek_social_wall_category_style', 'default'),
        'social_wall_cta_text' => get_option('vecura_mediathek_social_wall_cta_text', 'default'),

        // videothek
        'videothek_slider_aspect_ratio_width' => get_option('vecura_mediathek_videothek_slider_aspect_ratio_width'),
        'videothek_slider_aspect_ratio_height' => get_option('vecura_mediathek_videothek_slider_aspect_ratio_height'),
        'videothek_hero_aspect_ratio_width' => get_option('vecura_mediathek_videothek_hero_aspect_ratio_width'),
        'videothek_hero_aspect_ratio_height' => get_option('vecura_mediathek_videothek_hero_aspect_ratio_height'),


        // news 
        'news_source' => get_option('vecura_mediathek_news_source', 'cpt'),



        // Anbindungen 
        // YouTube 
        'youtube_api_key' => get_option('vecura_mediathek_youtube_api_key', ''),
        'youtube_channels'          => $youtube_channels, // Hier kommt jetzt ein Array rein

        // weitere Einstellungen...
    );

    $settings_options = array(
        'category_styles' => get_category_styles()
    );

    // Ajax-Informationen
    $ajax_info = array(
        'url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('vecura_mediathek_nonce')
    );

    ?>
    <!-- Dedizierter Notice-Bereich -->
    <div class="wrap" id="vecura-notices">
        <h1>MediaHub</h1>
        <!-- WordPress Notices erscheinen hier automatisch -->
    </div>
    <div class="wrap">
        <div id="app">
            <div class="vecura-header">
                <h2><?php _e('MediaHub', 'textdomain'); ?></h2>
                <ul class="tabs">
                    <li v-for="tab in availableTabs" :key="tab.id" @click="activeTab = tab.id" :class="{ active: activeTab === tab.id }">
                        {{ tab.label }}
                    </li>
                </ul>
            </div>

            <div>
                <p>activeTab: {{activeTab}}
            </div>

        
            <div v-if="activeTab === 'global'">
                <h2>Globale Einstellungen</h2>
                <form method="post" action="options.php">
                    <?php
                        // settings_fields('vecura_mediathek_settings_group');
                        // do_settings_sections('x-mediathek-settings');
                        // submit_button();
                    ?>
                    <div> 
                        <div class="flex-settings">
                            <label for="text-color">Text Color</label>
                            <input type="text" id="text-color" v-model="settings.text_color">
                        </div>
                        <div class="flex-settings">
                            <label for="accent-color-primary">Accent Color Primary</label>
                            <input type="text" id="accent-color-primary" v-model="settings.accent_color_primary">
                        </div>
                        <div class="flex-settings">
                            <label for="accent-color-secondary">Accent Color Secondary</label>
                            <input type="text" id="accent-color-secondary" v-model="settings.accent_color_secondary">
                        </div>
                        <div class="flex-settings">
                            <label for="accent-color-tertiary">Accent Color Thertiary</label>
                            <input type="text" id="accent-color-tertiary" v-model="settings.accent_color_tertiary">
                        </div>
                        <div class="flex-settings">
                            <label for="background-color">Background Color</label>
                            <input type="text" id="background-color" v-model="settings.background_color">
                        </div>
                    </div>
                </form>
            </div>
            
            
            
            <div v-if="activeTab === 'social-wall'">
                <h2>Social Wall Settings</h2>
                <div> 
                    <div class="flex-settings">
                        <label for="social-wall-hide-categories">Hide categories</label>
                        <input type="checkbox" id="social-wall-hide-categories" v-model="settings.social_wall_hide_categories">
                    </div>
                    <!-- <p>settings.social_wall_hide_categories: {{settings.social_wall_hide_categories}}<p> -->
                    
                    <div class="flex-settings">
                        <label for="social-wall-hide-date">Hide date</label>
                        <input type="checkbox" id="social-wall-hide-date" v-model="settings.social_wall_hide_date">
                    </div>
                    <p>settings.social_wall_hide_date: {{settings.social_wall_hide_date}}<p>

                    <div class="flex-settings">
                        <label for="category-style">Category Style</label>
                        <select id="category-style" v-model="settings.social_wall_category_style">
                            <option 
                                v-for="option in settings_options.category_styles"
                                :key="option.value" 
                                :value="option.value"
                            >
                                {{ option.label }}
                            </option>
                        </select>
                    </div>
                    <p>settings.social_wall_category_style: {{settings.social_wall_category_style}}<p>

                    <div class="flex-settings">
                        <label for="social-wall-cta-text">CTA-Text</label>
                        <input type="text" id="social-wall-cta-text" v-model="settings.social_wall_cta_text">
                    </div>
                    <p>settings.social_wall_cta_text: {{settings.social_wall_cta_text}}</p>

                </div>
            </div>


            <div v-if="activeTab === 'videothek'">
                <h2>Videothek Settings</h2>
                <div> 
                    <!--
                    'videothek_slider_aspect_ratio_width' => get_option('vecura_mediathek_videothek_slider_aspect_ratio_width', 'default'),
                    'videothek_slider_aspect_ratio_height' => get_option('vecura_mediathek_videothek_slider_aspect_ratio_height', 'default'),
                    'videothek_hero_aspect_ratio_width' => get_option('vecura_mediathek_videothek_hero_aspect_ratio_width', 'default'),
                    'videothek_hero_aspect_ratio_height' => get_option('vecura_mediathek_videothek_hero_aspect_ratio_height', 'default'),
                    -->
                    <div class="flex-settings">
                        <label for="social-wall-cta-text">CTA-Text</label>
                        <input type="text" id="social-wall-cta-text" v-model="settings.videothek_slider_aspect_ratio_width">
                    </div>
                    <p>settings.videothek_slider_aspect_ratio_width: {{settings.videothek_slider_aspect_ratio_width}}</p>
                    
                    <div class="flex-settings">
                        <label for="social-wall-cta-text">CTA-Text</label>
                        <input type="text" id="social-wall-cta-text" v-model="settings.videothek_slider_aspect_ratio_height">
                    </div>
                    <p>settings.videothek_slider_aspect_ratio_height: {{settings.videothek_slider_aspect_ratio_height}}</p>

                    <div class="flex-settings">
                        <label for="social-wall-cta-text">CTA-Text</label>
                        <input type="text" id="social-wall-cta-text" v-model="settings.videothek_hero_aspect_ratio_width">
                    </div>
                    <p>settings.videothek_hero_aspect_ratio_width: {{settings.videothek_hero_aspect_ratio_width}}</p>

                    <div class="flex-settings">
                        <label for="social-wall-cta-text">CTA-Text</label>
                        <input type="text" id="social-wall-cta-text" v-model="settings.videothek_hero_aspect_ratio_height">
                    </div>
                    <p>settings.videothek_hero_aspect_ratio_height: {{settings.videothek_hero_aspect_ratio_height}}</p>

                </div>
            </div>


            <div v-if="activeTab === 'news'">
                <h2>News Einstellungen</h2>
                <div class="flex-settings">
                    <label for="news-source">News Quelle</label>
                    <select id="news-source" v-model="settings.news_source">
                        <option value="cpt">Eigene News (Custom Post Type)</option>
                        <option value="posts">Blogbeiträge als News nutzen</option>
                    </select>
                </div>
                <p>settings.news_source: {{settings.news_source}}</p>
            </div>


            <div v-if="activeTab === 'integrations'">
                <h2>Integrations</h2>

                <h3>Youtube</h3>
                <form method="post" action="options.php">
                    <?php
                        // settings_fields('vecura_mediathek_settings_group');
                        // do_settings_sections('x-mediathek-settings');
                        // submit_button();
                    ?>
                    <div class="flex-settings">
                        <label for="youtube-api-key">YouTube API Key</label>
                        <!-- v-model anpassen an den richtigen Schlüssel -->
                        <input type="text" id="youtube-api-key" v-model="settings.youtube_api_key">
                    </div>
                    <!-- <div class="flex-settings">
                        <label for="youtube-channels">
                        YouTube Channels (Channel IDs, kommasepariert)<br>
                        Beispiel: UC123abc,UC456def
                        </label>
                        <input type="text" id="youtube-channels" v-model="settings.youtube_channels">
                    </div> -->
                    <div id="channel-search-app">
                        <h2>YouTube Channel Suche</h2>
                        <p>
                            Gib einen Channel-Namen ein und klicke auf "Suchen":
                        </p>
                        <input type="text" v-model="youtube_channel_name" placeholder="Channel Name">
                        <div class="btn black" @click="youtubeSearchChannel">Suchen</div>
                        
                        <!-- Suchergebnisse anzeigen -->
                        <div v-if="youtube_search_results.length > 0">
                            <h3>Suchergebnisse:</h3>
                            <div v-for="(result, index) in youtube_search_results" :key="result.id.channelId" class="channel-result" style="border: 1px solid #ccc; padding: 10px; margin-bottom: 5px;">
                            <p><strong>{{ result.snippet.title }}</strong></p>
                            <p><em>Channel ID:</em> {{ result.id.channelId }}</p>
                            <p>{{ result.snippet.description || 'Keine Beschreibung' }}</p>
                            <div class="btn black" @click="youtubeAddChannel(result)">Kanal hinzufügen</div>
                            </div>
                        </div>
                        
                        <!-- Liste der ausgewählten Kanäle -->
                        <div v-if="settings.youtube_channels.length > 0">
                            <h3>Verbundene Kanäle:</h3>
                            <ul>
                                <li v-for="(chan, index) in settings.youtube_channels" :key="chan.id">
                                    <strong>{{ chan.snippet.title }}</strong> ({{ chan.id }})
                                    <div class="btn black" @click="youtubeRemoveChannel(index)">Entfernen</div>
                                </li>
                            </ul>
                        </div>
                        
                        <div v-if="youtube_error" style="color: red;">{{ youtube_error }}</div>
                    </div>
                </form>
            </div>


            <div v-if="activeTab === 'draggable'">
                <h2>Whatever</h2>
                <draggable 
                    v-model="myArray" 
                    :item-key="'id'" 
                    @start="drag=true" 
                    @end="drag=false"
                    item-class="draggable-item"
                    handle=".drag-handle"
                >
                    <template #item="{element}">
                        <div>
                            <div>
                                {{element.name}}
                            </div>
                            <div class="drag-handle">B</div>
                        </div>
                    </template>
                </draggable>
            </div>


            <!-- global Save-Button -->
            <div class="btn black save-button" @click="saveSettings">
                SAVE Settings
            </div>
        </div>
        <script type="module">
            const { createApp, ref, onMounted, onUpdated, onUnmounted, nextTick, watch, computed } = Vue

            
            const app = createApp({
                setup() {
                    
                    // Get settings from WP with Vue  
                    // Get Data from php var
                    // const settings = ref(<?php echo json_encode($settings); ?>)

                    // Hilfsfunktion zur Transformation der Werte
                    const transformSettings = (settings) => {
                        console.log('%c 📞 -> transformSettings', 'color: green;')
                        const transformed = { ...settings }
                        
                        Object.keys(transformed).forEach(key => {
                            // Prüfen auf String '1' oder '0' und numerische 1 oder 0
                            if (transformed[key] === '1' || transformed[key] === '0' || 
                                transformed[key] === 1 || transformed[key] === 0) {
                                // Erst zu Number konvertieren, dann zu Boolean
                                transformed[key] = Boolean(Number(transformed[key]))
                            }
                        })
                        
                        console.log('💾 -> settings -> transformed: ', transformed)
                        return transformed
                    }

                    const settings = ref(transformSettings(<?php echo json_encode($settings); ?>))
                    const settings_options = ref(<?php echo json_encode($settings_options); ?>)

                    // Funktion zum Speichern der Einstellungen
                    const ajaxInfo = <?php echo json_encode($ajax_info); ?>

                    const saveSettings = async () => {
                        console.log('%c 📞 -> saveSettings', 'color: green;')
                        console.log('💾 -> JSON.stringify(settings.value)', JSON.stringify(settings.value))

                        console.log('settings.value: ', settings.value)

                        const settingsToSave = Object.entries(settings.value).reduce((acc, [key, value]) => {
                            acc[key] = typeof value === 'boolean' ? (value ? 1 : 0) : value
                            return acc
                        }, {})

                        try {
                            const response = await fetch(ajaxInfo.url, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/x-www-form-urlencoded',
                                },
                                body: new URLSearchParams({
                                    action: 'vecura_save_settings',
                                    nonce: ajaxInfo.nonce,
                                    // settings: JSON.stringify(settings.value)
                                    settings: JSON.stringify(settingsToSave)
                                })
                            })

                            const data = await response.json()
                            console.log('Komplette Server-Antwort:', data)
                            
                            if (data.success) {
                                console.log('Debug Schritte:', data.data)
                                console.table(data.data.step3_updates)
                            } else {
                                console.error('Fehler beim Speichern:', data)
                            }
                        } catch (error) {
                            console.error('Fetch Error:', error)
                        }
                    }

                    // Automatisches Speichern bei Änderungen
                    // watch(settings, (newVal) => {
                    //     saveSettings()
                    // }, { deep: true })


                    /**
                     * Integrations
                     */
                    // Youtube
                    const youtube_channel_name = ref('');
                    const youtube_search_results = ref([]);
                    // const youtube_channels = ref([]);
                    const youtube_error = ref('');
                    
                    // API-Key aus den WP-Einstellungen (per wp_localize_script)
                    const apiKey = settings.value.youtube_api_key;

                    // Sucht Kanäle anhand eines eingegebenen Namens (Suchbegriff wird hier über "q" abgefragt)
                    async function youtubeSearchChannel() {
                        console.log('youtube_channel_name.value: ', youtube_channel_name.value)
                        console.log('settings.value.youtube_api_key: ', settings.value.youtube_api_key)

                        youtube_error.value = '';
                        youtube_search_results.value = [];
                        if (youtube_channel_name.value.trim() === '') {
                            youtube_error.value = 'Bitte gib einen Channel-Namen ein.';
                            return;
                        }
                        try {
                            const response = await fetch(
                                `https://www.googleapis.com/youtube/v3/search?part=snippet&type=channel&q=${encodeURIComponent(youtube_channel_name.value)}&key=${settings.value.youtube_api_key}`
                            );
                            if (!response.ok) {
                                throw new Error("HTTP-Fehler: " + response.status);
                            }
                            const data = await response.json();
                            if (data.items && data.items.length > 0) {
                                youtube_search_results.value = data.items;
                            } else {
                                youtube_error.value = "Keine Kanäle gefunden für: " + youtube_channel_name.value;
                            }
                        } catch (err) {
                            youtube_error.value = "Fehler: " + err.message;
                        }
                    }

                    // Fügt einen gefundenen Kanal zur Liste hinzu, sofern noch nicht vorhanden
                    function youtubeAddChannel(channel) {
                        if (!settings.value.youtube_channels.find(ch => ch.id === channel.id.channelId)) {
                            const newChannel = {
                                id: channel.id.channelId,
                                snippet: channel.snippet
                            };
                            settings.value.youtube_channels.push(newChannel);
                        }
                    }

                    // Entfernt einen Kanal aus der Auswahl
                    function youtubeRemoveChannel(index) {
                        settings.value.youtube_channels.splice(index, 1);
                    }
                    

                    const availableTabs = [
                        {
                            id: 'global',
                            label: 'Global'
                        },
                        {
                            id: 'social-wall',
                            label: 'Social Wall'
                        },
                        {
                            id: 'videothek',
                            label: 'Videothek'
                        },
                        {
                            id: 'news',
                            label: 'News'
                        },
                        {
                            id: 'integrations',
                            label: 'Integrations'
                        },
                        {
                            id: 'draggable',
                            label: 'DRaggable Test'
                        },
                    ]

                    const activeTab = ref('global')


                    const myArray = ref([
                        { id: 1, name: 'Element 1' },
                        { id: 2, name: 'Element 2' },
                        { id: 3, name: 'Element 3' },
                    ]);
                    return {
                        availableTabs,
                        activeTab,
                        myArray,


                        // youtube
                        youtube_channel_name,
                        youtube_search_results,
                        // youtube_channels,
                        youtube_error,
                        youtubeSearchChannel,
                        youtubeAddChannel,
                        youtubeRemoveChannel,

                        // settings from WP
                        settings_options,
                        settings,
                        saveSettings,


                        // Andere Daten und Funktionen hier
                    }
                }
            })
            app.component('draggable', vuedraggable);
            app.mount('#app')
        </script>

        <style>
            .vecura-header {
                display: flex;
                gap: 1rem;
                align-items: center;
            }
            .vecura-header h1 {
                margin: 0;
                padding: 0;
            }
            .vecura-header ul.tabs {
                display: flex;
                gap: 1rem;
            }
            .vecura-header ul.tabs li {
                margin: 0;
                padding: 0.5rem 1rem;
                border-radius: 0.5rem;
                font-size: 1rem;
                font-weight: 500;
                cursor: pointer;
            }
            .vecura-header ul.tabs li.active {
                background: #dedede;
            }
            .flex-settings:first-child {
                border-top: 1px solid #dedede;
            }
            .flex-settings {
                padding: 1rem 0;
                border-bottom: 1px solid #dedede;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .draggable-item {
                padding: 10px;
                margin: 5px;
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                cursor: move; 
                user-select: none;
            }

            .draggable-item:hover {
                background-color: #e9e9e9;
            }

            .draggable-item.dragging {
                opacity: 0.5;
            }


            .btn {
                display: inline-block;
                margin: 1rem 0;
                padding: 0.5rem 1rem;
                border-radius: 0.5rem;
                font-size: 1rem;
                font-weight: 500;
                cursor: pointer;
            }
            .btn.black {
                background: #123;
                color: #fff;
            }
        </style>
        
        <script src="<?php echo plugin_dir_url(__FILE__) . '../../includes/js/vue/vue.global.js'; ?>"></script>
        <script src="<?php echo plugin_dir_url(__FILE__) . '../../includes/js/vue/Sortable.min.js'; ?>"></script>
        <script src="<?php echo plugin_dir_url(__FILE__) . '../../includes/js/vue/vuedraggable.umd.js'; ?>"></script>

    </div>
    <?php
}