<?php
// admin/class-admin.php

// Imports
require_once plugin_dir_path(__FILE__) . 'settings/settings-options.php';
require_once plugin_dir_path(__FILE__) . 'settings/settings-registration.php';
require_once plugin_dir_path(__FILE__) . 'settings/settings-page.php';


class Vecura_Mediathek_Admin {
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'init_settings'));
    }

    public function add_admin_menu() {
        /*
        // Hauptmenüpunkt
        add_menu_page(
            __('vecura MediaHub', 'textdomain'),
            __('vecura MediaHub', 'textdomain'),
            'manage_options',
            'vecura-mediahub-settings',
            array($this, 'render_settings_page'), // Ruft die Funktion aus settings-page.php auf
            'dashicons-admin-media',
            6
        );

        Unter<PERSON><PERSON>s für CPTs
        add_submenu_page(
            'vecura-mediahub-settings',
            __('Social Wall', 'textdomain'),
            __('Social Wall', 'textdomain'),
            'manage_options',
            'edit.php?post_type=social_wall'
        );

        add_submenu_page(
            'vecura-mediahub-settings',
            __('Videothek', 'textdomain'),
            __('Videothek', 'textdomain'),
            'manage_options',
            'edit.php?post_type=videothek'
        );

        add_submenu_page(
            'vecura-mediahub-settings',
            __('News', 'textdomain'),
            __('News', 'textdomain'),
            'manage_options',
            'edit.php?post_type=news'
        );
        */

        $main_menu_slug = 'vecura-mediahub-settings';

        // Hauptmenü
        add_menu_page(
            'vecura MediaHub',
            'vecura MediaHub',
            'manage_options',
            $main_menu_slug,
            array($this, 'render_settings_page'),
            'dashicons-admin-media',
            25
        );

        $news_source = get_option('vecura_mediathek_news_source', 'cpt');

        $cpts = [
            'social_wall' => 'Social Wall',
            'videothek'   => 'Videothek',
            'news'        => 'News'
        ];

        foreach ($cpts as $slug => $label) {
            // Falls "News" auf "posts" gesetzt ist, leite es zur Beitragsseite um
            $post_type_slug = ($slug === 'news' && $news_source === 'posts') ? 'post' : $slug;

            // CPT Hauptmenü als Untermenü von vecura-mediahub
            add_submenu_page(
                $main_menu_slug,
                $label,
                $label,
                'manage_options',
                "vecura-{$slug}",
                function() use ($post_type_slug) {
                    wp_redirect(admin_url("edit.php?post_type={$post_type_slug}"));
                    exit;
                }
            );

            // Untermenüs für jeden CPT
            add_submenu_page(
                "vecura-{$slug}",  
                "Alle {$label}",
                "Alle {$label}",
                'manage_options',
                "edit.php?post_type={$post_type_slug}"
            );

            add_submenu_page(
                "vecura-{$slug}",
                "Neue {$label}",
                'Neu hinzufügen',
                'manage_options',
                "post-new.php?post_type={$post_type_slug}"
            );

            add_submenu_page(
                "vecura-{$slug}",
                "{$label} Taxonomien",
                'Taxonomien verwalten',
                'manage_options',
                "manage_{$slug}_taxonomies",
                "manage_{$slug}_taxonomies_page"
            );
        }
    }

    public function render_social_wall_page() {
        echo '<div class="wrap">';
            echo '<h1>Social Wall Verwaltung</h1>';

            echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">';
            
                // Statistiken
                $posts = wp_count_posts('social_wall');
                $taxonomies = get_object_taxonomies('social_wall', 'objects');
                
                echo '<div class="card" style="max-width: unset;">';
                    echo '<h2>Übersicht</h2>';
                    echo '<p>Veröffentlichte Beiträge: ' . $posts->publish . '</p>';
                    echo '<p>Entwürfe: ' . $posts->draft . '</p>';
                    
                    if (!empty($taxonomies)) {
                        echo '<h3>Taxonomien</h3>';
                        echo '<ul>';
                        foreach ($taxonomies as $tax) {
                            $terms = wp_count_terms($tax->name);
                            echo '<li>' . $tax->label . ': ' . $terms . ' Begriffe</li>';
                        }
                        echo '</ul>';
                    }
                echo '</div>';

                // Quick-Links
                echo '<div class="card" style="max-width: unset;">';
                    echo '<h2>Schnellzugriff</h2>';
                    echo '<p><a href="post-new.php?post_type=social_wall" class="button">Neuen Beitrag erstellen</a></p>';
                    echo '<p><a href="edit.php?post_type=social_wall" class="button">Alle Beiträge anzeigen</a></p>';
                    echo '<p><a href="edit.php?post_type=social_wall&page=manage_social_wall_taxonomies" class="button">Taxonomien verwalten</a></p>';
                echo '</div>';
            
            echo '</div>';


            // Beitragstabelle
            $posts = get_posts([
                'post_type' => 'social_wall',
                'posts_per_page' => -1,
                'orderby' => 'date',
                'order' => 'DESC'
            ]);


            if ($posts) {
                echo '<h2>Beiträge</h2>';
                echo '<table class="wp-list-table widefat fixed striped">';
                echo '<thead><tr>
                        <th>Titel</th>
                        <th>Status</th>
                        <th>Datum</th>
                        <th>Aktionen</th>
                    </tr></thead><tbody>';

                foreach ($posts as $post) {
                    $edit_link = get_edit_post_link($post->ID);
                    $view_link = get_permalink($post->ID);
                    $quick_edit_link = "post.php?post={$post->ID}&action=edit";
                    
                    echo "<tr>";
                    echo "<td><strong><a href='{$edit_link}'>{$post->post_title}</a></strong></td>";
                    echo "<td>" . get_post_status_object($post->post_status)->label . "</td>";
                    echo "<td>" . get_the_date('', $post) . "</td>";
                    echo "<td class='action-links'>";
                    echo "<a href='{$edit_link}'>Bearbeiten</a> | ";
                    echo "<a href='{$view_link}' target='_blank'>Ansehen</a>";
                    echo "</td>";
                    echo "</tr>";
                }

                echo '</tbody></table>';
            }

        echo '</div>';
    }

    public function init_settings() {
        // Lädt und initialisiert die Einstellungen aus settings-registration.php
        if (function_exists('vecura_register_settings')) {
            vecura_register_settings();
        }
    }

    public function render_settings_page() {
        // Lädt und rendert die Settings-Page aus settings-page.php
        if (function_exists('vecura_render_settings_page')) {
            vecura_render_settings_page();
        } else {
            // Fallback-Inhalt, falls die Funktion nicht gefunden wird
            echo '<p>Funktion nicht gefunden</p>';
        }
    }
}

// Initialisiere die Admin-Klasse
new Vecura_Mediathek_Admin();