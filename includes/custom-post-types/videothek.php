<?php
/**
 * Registriert CPT social wall
 */

// Verhindert direkten Zugriff
if (!defined('ABSPATH')) {
    exit;
}

function register_videothek() {
    register_post_type('videothek', [
        'labels' => [
            'name' => __('Videothek', 'textdomain'),
            'singular_name' => __('Videothek', 'textdomain')
        ],
        'public' => true,
        'has_archive' => true,
        'rewrite' => array('slug' => 'videothek'),
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'show_in_rest' => true,
        // 'show_in_menu' => false 
        // 'show_in_menu' => 'vecura-mediahub-settings',
        // 'show_in_admin_bar' => true
    ]);
}
add_action('init', 'register_videothek');



/**
 * Videothek meta Box basic
 */

 /* 
// Fügt eine Meta Box zur "Videothek" CPT hinzu.
function add_videothek_video_meta_box() {
    add_meta_box(
        'videothek_video_meta',           // ID der Meta Box
        __('Video auswählen', 'textdomain'), // Titel der Meta Box
        'render_videothek_video_meta_box', // Callback-Funktion
        'videothek',                      // Post Type
        'side',                            // Position (normal, side, advanced)
        'high'                             // Priorität
    );
}
add_action('add_meta_boxes', 'add_videothek_video_meta_box');

// Render Video Meta box
function render_videothek_video_meta_box($post) {
    // Hole gespeicherten Wert
    $video_url = get_post_meta($post->ID, '_videothek_video_url', true);

    // Sicherheits-Nonce
    wp_nonce_field('videothek_video_nonce', 'videothek_video_nonce_field');

    echo '<p>' . __('Wähle ein Video aus der Mediathek oder füge eine URL ein.', 'textdomain') . '</p>';

    echo '<input type="text" id="videothek_video_url" name="videothek_video_url" value="' . esc_attr($video_url) . '" style="width: 100%;" placeholder="https://example.com/video.mp4" />';
    echo '<button type="button" class="button videothek-video-select">' . __('Video auswählen', 'textdomain') . '</button>';
    
    if ($video_url) {
        echo '<p><video width="100%" controls><source src="' . esc_url($video_url) . '" type="video/mp4"></video></p>';
    }

    // Das Inline-Skript direkt hier platzieren
    echo '<script>
        jQuery(document).ready(function($) {
            let file_frame;
            $(".videothek-video-select").on("click", function(e) {
                e.preventDefault();
                if (file_frame) { file_frame.open(); return; }
                file_frame = wp.media.frames.file_frame = wp.media({
                    title: "Wähle ein Video",
                    button: { text: "Video auswählen" },
                    library: { type: "video" },
                    multiple: false
                });
                file_frame.on("select", function() {
                    let attachment = file_frame.state().get("selection").first().toJSON();
                    $("#videothek_video_url").val(attachment.url);
                });
                file_frame.open();
            });
        });
    </script>';
}


// Speichert die Meta Box-Daten.
function save_videothek_video_meta($post_id) {
    // Sicherheitsprüfungen
    if (!isset($_POST['videothek_video_nonce_field']) || 
        !wp_verify_nonce($_POST['videothek_video_nonce_field'], 'videothek_video_nonce')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichere die Video-URL
    if (isset($_POST['videothek_video_url'])) {
        update_post_meta($post_id, '_videothek_video_url', esc_url_raw($_POST['videothek_video_url']));
    } else {
        delete_post_meta($post_id, '_videothek_video_url');
    }
}
add_action('save_post', 'save_videothek_video_meta');
*/


/**
 * 
 * 
 * VIDEOTHEK META BOX FROM rr
 * 
 * 
 */
/**
 * Funktion zum Hinzufügen der Metabox für Video CPT Hero
 */
function add_video_metabox() {
    $post_types = ['videothek']; // Die Post-Typen, auf die die Metabox angewendet werden soll

    foreach ($post_types as $post_type) {
        add_meta_box(
            'video_metabox', // ID der Metabox
            __('Video Settings', 'text-domain'), // Titel der Metabox
            'render_video_metabox', // Callback-Funktion, die das HTML der Metabox rendert
            $post_type, // Post-Typ, auf den die Metabox angewendet wird
            'advanced', // Position (normal, side, advanced)
            'high' // Priorität (high, default, low)
        );
    }
}
add_action('add_meta_boxes', 'add_video_metabox', 1);

function create_youtube_table_if_not_exists() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'youtube_data';

    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            kind varchar(255) NOT NULL,
            etag varchar(255) NOT NULL,
            video_id varchar(255) NOT NULL,
            id_kind varchar(255) NOT NULL,
            published_at datetime NOT NULL,
            channel_id varchar(255) NOT NULL,
            title text NOT NULL,
            description text NOT NULL,
            thumbnail_default_url varchar(255) NOT NULL,
            thumbnail_medium_url varchar(255) NOT NULL,
            thumbnail_high_url varchar(255) NOT NULL,
            channel_title varchar(255) NOT NULL,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}
add_action('init', 'create_youtube_table_if_not_exists');

// PHP to retrieve JSON Data from JS
function insert_youtube_data_ajax() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'youtube_data';

    // Prüfe, ob die Daten gesendet wurden
    if (!isset($_POST['data'])) {
        wp_send_json_error('No data received');
        return;
    }

    // Dekodiere die JSON-Daten
    $data = json_decode(stripslashes($_POST['data']), true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        wp_send_json_error('JSON decoding error: ' . json_last_error_msg());
        return;
    }

    // Lösche alle vorhandenen Daten in der Tabelle
    $wpdb->query("DELETE FROM $table_name");

    foreach ($data['items'] as $item) {
        $result = $wpdb->insert(
            $table_name,
            array(
                'kind' => isset($item['kind']) ? $item['kind'] : '',
                'etag' => isset($item['etag']) ? $item['etag'] : '',
                'video_id' => isset($item['id']['videoId']) ? $item['id']['videoId'] : '',
                'id_kind' => isset($item['id']['kind']) ? $item['id']['kind'] : '',
                'published_at' => isset($item['snippet']['publishedAt']) ? $item['snippet']['publishedAt'] : '',
                'channel_id' => isset($item['snippet']['channelId']) ? $item['snippet']['channelId'] : '',
                'title' => isset($item['snippet']['title']) ? $item['snippet']['title'] : '',
                'description' => isset($item['snippet']['description']) ? $item['snippet']['description'] : '',
                'thumbnail_default_url' => isset($item['snippet']['thumbnails']['default']['url']) ? $item['snippet']['thumbnails']['default']['url'] : '',
                'thumbnail_medium_url' => isset($item['snippet']['thumbnails']['medium']['url']) ? $item['snippet']['thumbnails']['medium']['url'] : '',
                'thumbnail_high_url' => isset($item['snippet']['thumbnails']['high']['url']) ? $item['snippet']['thumbnails']['high']['url'] : '',
                'channel_title' => isset($item['snippet']['channelTitle']) ? $item['snippet']['channelTitle'] : ''
            )
        );

        if ($result === false) {
            wp_send_json_error('Database insert error: ' . $wpdb->last_error);
            return;
        }
    }


    wp_send_json_success('Data inserted successfully');
}
add_action('wp_ajax_insert_youtube_data', 'insert_youtube_data_ajax');
add_action('wp_ajax_nopriv_insert_youtube_data', 'insert_youtube_data_ajax');

// Get youtube data from db 
function get_youtube_data_ajax() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'youtube_data';

    $results = $wpdb->get_results("SELECT * FROM $table_name", ARRAY_A);

    if (empty($results)) {
        wp_send_json_error('No data found');
        return;
    }

    // Baue das JSON-Format so auf, wie es von der YouTube API erwartet wird
    $youtube_data = array(
        "kind" => "youtube#searchListResponse",
        "etag" => "example_etag",
        "regionCode" => "DE",
        "pageInfo" => array(
            "totalResults" => count($results),
            "resultsPerPage" => count($results)
        ),
        "items" => array()
    );

    foreach ($results as $row) {
        $youtube_data['items'][] = array(
            "kind" => $row['kind'],
            "etag" => $row['etag'],
            "id" => array(
                "kind" => $row['id_kind'],
                "videoId" => $row['video_id']
            ),
            "snippet" => array(
                "publishedAt" => $row['published_at'],
                "channelId" => $row['channel_id'],
                "title" => $row['title'],
                "description" => $row['description'],
                "thumbnails" => array(
                    "default" => array(
                        "url" => $row['thumbnail_default_url'],
                        "width" => 120,
                        "height" => 90
                    ),
                    "medium" => array(
                        "url" => $row['thumbnail_medium_url'],
                        "width" => 320,
                        "height" => 180
                    ),
                    "high" => array(
                        "url" => $row['thumbnail_high_url'],
                        "width" => 480,
                        "height" => 360
                    )
                ),
                "channelTitle" => $row['channel_title'],
                "liveBroadcastContent" => "none",
                "publishTime" => $row['published_at']
            )
        );
    }

    wp_send_json_success($youtube_data);
}

add_action('wp_ajax_get_youtube_data', 'get_youtube_data_ajax');
add_action('wp_ajax_nopriv_get_youtube_data', 'get_youtube_data_ajax');

// Funktion zum Rendern der Metabox
function render_video_metabox($post) {
    // Hole den YouTube API-Key
    $youtube_api_key = get_option('vecura_mediathek_youtube_api_key', '');

    // Hole die gespeicherten Channels (als JSON-String) und dekodiere ihn in ein Array
    $youtube_channels_json = get_option('vecura_mediathek_youtube_channels', '');
    $youtube_channels = !empty($youtube_channels_json) ? json_decode($youtube_channels_json, true) : array();
    if ( ! is_array($youtube_channels) ) {
        $youtube_channels = array();
    }

    // Video-Auswahl
    $use_youtube_frame = get_post_meta($post->ID, 'use_youtube_frame', true);

    $video_id = get_post_meta($post->ID, 'selected_video_id', true);
    $video_url = wp_get_attachment_url($video_id);

    // YouTube-URL auslesen
    $youtube_video_url = get_post_meta($post->ID, 'youtube_video_url', true);
    ?>

    <!-- Headline-Eingabefeld -->
    <p>
        <label for="video_headline">Hero Headline:</label><br>
        <input 
            type="text" 
            id="video_headline" 
            name="video_headline" 
            value="<?php echo esc_attr(get_post_meta($post->ID, 'video_headline', true)); ?>"
            style="width: 100%; font-size: 24px;">
    </p>

    <label for="use_youtube_frame">Use Youtube instead of self hosted video</label>
    <input type="checkbox" id="use_youtube_frame" name="use_youtube_frame" value="1" <?php checked($use_youtube_frame, '1'); ?>>
    <!-- <p>$use_youtube_frame: <?php echo $use_youtube_frame; ?></p> -->

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const useYoutubeVideoCheckbox = document.getElementById('use_youtube_frame');
            const youtubeSelector = document.querySelector('.youtube-selector');
            const videoSelector = document.querySelector('.video-selector');

            // Funktion zum Umschalten der Anzeige basierend auf dem Status der Checkbox
            function toggleVideoSource() {
                if (useYoutubeVideoCheckbox.checked) {
                    youtubeSelector.classList.remove('hidden');
                    videoSelector.classList.add('hidden');
                } else {
                    youtubeSelector.classList.add('hidden');
                    videoSelector.classList.remove('hidden');
                }
            }

            // Initiales Umschalten beim Laden der Seite
            toggleVideoSource();

            // Event Listener hinzufügen, um das Umschalten bei Änderungen der Checkbox zu ermöglichen
            useYoutubeVideoCheckbox.addEventListener('change', toggleVideoSource);
        });
    </script>

    <!-- <div class="mediathek-selector video-selector <?php echo $use_youtube_frame === '0' ? '' : 'hidden'; ?>"> -->
    <div class="mediathek-selector video-selector hidden">
        <label for="selected_video_id">Video:</label><br>
        <input type="hidden" id="selected-video-id" name="selected_video_id" value="<?php echo esc_attr($video_id); ?>">
        <?php if (!empty($video_url)) : ?>
            <video src="<?php echo esc_url($video_url); ?>" controls width="320"></video>
        <?php endif; ?>
        <button type="button" id="select-video-button" class="button">Video auswählen</button>
    </div>

    <script type="text/javascript">
        let ajax_object = <?php echo json_encode(array('ajax_url' => admin_url('admin-ajax.php'))); ?>;

        // select video from wp-mediathek
        jQuery(document).ready(function($) {
            console.log('%c video cpt script loaded', 'color: green;')

            $('#select-video-button').on('click', function() {
                var send_attachment_bkp = wp.media.editor.send.attachment;
                wp.media.editor.send.attachment = function(props, attachment) {
                    $('#selected-video-id').val(attachment.id);
                    var video_url = attachment.url;
                    $('#selected-video-id').siblings('video').attr('src', video_url).show();
                    wp.media.editor.send.attachment = send_attachment_bkp;
                }
                wp.media.editor.open($(this));
                return false;
            });

            // Event-Handler für das Auswählen von Thumbnails
            console.log('%c allow only images', 'color: red;')
            /* 
            $('#select-thumbnail-button').on('click', function() {
                var send_attachment_bkp = wp.media.editor.send.attachment;
                wp.media.editor.send.attachment = function(props, attachment) {
                    // Stelle sicher, dass nur Bilder ausgewählt werden können
                    if (attachment.type === 'image') {
                        $('#selected-thumbnail-id').val(attachment.id);
                        var thumbnail_url = attachment.url;
                        // Füge das Bild-Element hinzu oder aktualisiere es
                        var image_element = $('#selected-thumbnail-id').siblings('img');
                        if (image_element.length === 0) {
                            $('#selected-thumbnail-id').parent().append('<img src="' + thumbnail_url + '" width="320">');
                        } else {
                            image_element.attr('src', thumbnail_url);
                        }
                    } else {
                        alert('Bitte wähle ein Bild aus.');
                    }
                    wp.media.editor.send.attachment = send_attachment_bkp;
                }
                wp.media.editor.open($(this));
                return false;
            });
            */
            $('#select-thumbnail-button').on('click', function() {
                // Sichere die ursprüngliche send.attachment-Funktion
                var send_attachment_bkp = wp.media.editor.send.attachment;

                // Konfiguriere den Medien-Uploader
                var file_frame = wp.media.frames.file_frame = wp.media({
                    title: 'Wähle ein Thumbnail',  // Titel des Medien-Uploaders
                    button: {
                        text: 'Verwende dieses Bild' // Text des Bestätigungsbuttons
                    },
                    library: {
                        type: 'image' // Beschränkt die Mediathek auf Bilder
                    },
                    multiple: false // Erlaubt die Auswahl nur eines Bildes
                });

                // Event, das ausgelöst wird, wenn ein Bild ausgewählt wird
                file_frame.on('select', function() {
                    // Hole die Informationen zum ausgewählten Bild
                    var attachment = file_frame.state().get('selection').first().toJSON();

                    // Setze die Bild-ID und -URL im versteckten Feld und Bild-Tag
                    $('#selected-thumbnail-id').val(attachment.id);
                    var thumbnail_url = attachment.url;
                    var image_element = $('#selected-thumbnail-id').siblings('img');
                    if (image_element.length === 0) {
                        $('#selected-thumbnail-id').parent().append('<img src="' + thumbnail_url + '" width="320">');
                    } else {
                        image_element.attr('src', thumbnail_url);
                    }

                    // Stelle die ursprüngliche send.attachment-Funktion wieder her
                    wp.media.editor.send.attachment = send_attachment_bkp;
                });

                // Öffne den Medien-Uploader
                file_frame.open();
                return false;
            });
        });

    </script>

    <!-- Youtube-Video-Eingabefeld -->
    <!-- <div class="youtube-selector video-selector <?php echo $use_youtube_frame === '1' ? '' : 'hidden'; ?>"> -->
    <div class="youtube-selector video-selector hidden">
        <script src="https://apis.google.com/js/api.js"></script>

        

        <div class="channel-response-wrapper">
            <h2 style="padding: 20px 0; font-size: 24px;">Youtube Daten</h2>
            <div class="channel-response"></div>
        </div>

        <div id="spin" style="border-top-color: #49b2a9"></div>

        <!-- YouTube iFrame Player -->
        <style> 
            /* Spinner */
            #spin {
                display: block;
                width: 40px;
                height: 40px;
                margin: calc(10vh - 40px) auto;
                border: 3px solid transparent;
                border-radius: 50%;
                animation: spin 1s ease infinite;
                @apply border-t-darkprimary;
            }

            @keyframes spin {
                to {
                    transform: rotate(180deg);
                }
            }

            /* Meta box content */
            iframe#player {
                aspect-ratio: 16 / 9;
                max-width: 100%;
                height: auto;
            }

            /* #app */
            .video-item {
                background: #def0ee;
                border: solid 2px #def0ee;
                border-radius: 12px;
                overflow: hidden;
                padding-right: 20px;
                cursor: pointer;
            }
            .video-item.active {
                background: #def0ee;
                border: solid 2px #49b2a9;
            }

            /* utility classes */
            .nw-button {
                cursor: pointer;
                background-color: #49b2a9;
                color: white;
                padding: 10px 20px;
                border: none;
                outline: none;
                border-radius: 4px;
                font-size: 16px;
            }

            .nw-button.active {
                border: solid 2px #ff0201;
            }

            .relative {
                position: relative;
            }

            .items-center {
                align-items: center;
            }
            .flex {
                display: flex;
            }
            .inline-flex {
                display: inline-flex;
            }
            .flex-grow {
                flex-grow: 1;
            }
            .justify-center {
                justify-content: flex-start;
            }

            .w-full {
                width: 100%;
            }

            .gap-s {
                gap: 16px;
            }

            .hidden {
                display: none !important;
            }

            .checkmark-for-choosen {
                position: absolute;
                color: #fff;
                background: radial-gradient(circle at 50% 50%, #49b2a9, transparent 66%);
                width: 100%;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
            }


            .channel-item {
                border: solid 2px rgba(0,0,0,0.3);
                border-radius: 12px;
                display: flex;
                gap: 1rem;
                overflow: hidden;
                padding: 1rem;
            }

            .channel-item.active {
                background: #73b740;
            }
        </style>

        <div id="meta-content" class="hidden">
            <!-- Youtube iFrame API -->
            <script src="https://www.youtube.com/iframe_api" async></script>
            <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
            
            <div id="player-wrapper">
                <div id="player"></div>
            </div>

            <script>
                // YouTube Video ID
                var videoId = '<?php echo esc_attr($youtube_video_url); ?>';
                console.log('%c VideoId', 'color: green; font-size: 24px;', videoId);

                if(!videoId) {
                    console.log('No youtube video id found');
                } else {
                    // Player erstellen
                    var player;
                    function onYouTubeIframeAPIReady() {
                        player = new YT.Player('player', {
                            height: 'auto',
                            width: '100%',
                            videoId: videoId,
                            playerVars: {
                                'playsinline': 1,
                                'controls': 1,
                                // 'mute': 1,
                            },
                            events: {
                                // 'onReady': onPlayerReady
                            }
                        });
                    }
                }

            </script>

            <!-- VUE -->
            <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
            <!-- TODO: use prod version for production -->
            <!-- <script src="https://unpkg.com/vue@3.4.27/dist/vue.global.prod.js"></script> -->

            <div id="app">

                <hr>

                <p>youtube_api_key: {{youtube_api_key}}</p>
                <div>
                    <p>youtube_channels</p>
                    <ul>
                        <li 
                            v-for="(channel, index) in youtube_channels" 
                            :key="channel.id" 
                            class="youtube channel-item" 
                            @click="toggleChannelSelection(channel.id)"
                            :class="{ active: selectedChannelIds.includes(channel.id) }"
                        >
                            <div class="logo">
                                <img :src="channel.snippet.thumbnails.default.url" alt="Channel Logo">
                            </div>
                            <div class="infos">
                                <h3>{{ channel.snippet.title }} ({{ channel.id }})</h3>
                                <p>{{ channel.snippet.description }}</p>
                            </div>
                        </li>
                        <li 
                            class="youtube channel-item" 
                            @click="toggleShowAllChannels()"
                            :class="{ active: isShowAllVideosActive }"
                        >
                            <div class="logo">
                                Zeige alle Channels
                            </div>
                            <div class="infos">
                                <h3>Zeige alle Channel</h3>
                            </div>
                        </li>
                    </ul>
                </div>

                <hr>

                <!-- <p>filteredYoutubeApiItems: {{ filteredYoutubeApiItems }}</p> -->

                <!-- <div v-if="youtubeApiData?.kind"> -->
                <div>

                    <div>
                        <!-- <p>selectedChannelIds: {{selectedChannelIds}}</p> -->
                        <label for="searchYoutube">Youtube Videos durchsuchen (aggregatedResponse)
                            <span v-if="searchChars">({{ filteredYoutubeApiItems.length }} Videos gefunden)</span>
                        </label><br>
                        <div class="flex gap-s">
                            <!-- Search data from YouTube -->
                            <div class="flex flex-grow">
                                <input 
                                    type="text" 
                                    id="searchYoutube"
                                    class="nw-button w-full"
                                    v-model="searchChars"
                                    style="flex-grow: 1; font-size: 16px;"
                                >
                                
                            </div>

                            <!-- Sync Data from YouTube -->
                            <span class="nw-button inline-flex items-center " @click="manualSyncData()">
                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.651 7.65a7.131 7.131 0 0 0-12.68 3.15M18.001 4v4h-4m-7.652 8.35a7.13 7.13 0 0 0 12.68-3.15M6 20v-4h4"/>
                                </svg>
                                Sync Data from YouTube
                            </span>

                            <!-- <span class="nw-button inline-flex items-center" :class="{ 'active': isShowAllVideosActive }" @click="isShowAllVideosActive = !isShowAllVideosActive">
                                Zeige alle Videos
                            </span> -->

                        </div>
                    </div>
                    <!-- <p>searchChars: {{searchChars}}</p> -->

                    <!-- <p>{{ youtubeApiData?.pageInfo }}</p> -->

                    <!-- <h2 v-if="choosenVideo">{{ choosenVideo }}</h2> -->

                    <hr>

                    <!--
                    <div>
                    <p>youtubeApiData:<p>
                    <ul>
                        <li v-for="singleYouTubeApiData in youtubeApiData" :key="">
                    </ul>
                    </div>
                    -->

                    <hr>
                    
                    <div class="youtube-api-data">
                        <ul>
                            <li 
                                v-for="(item, index) in filteredYoutubeApiItems" 
                                :key="index" 
                                class="video-item"
                                :class="{ 'active': youtubeId === item.id.videoId }"
                                @click="useVideo(item)"
                            >
                                <!-- v-if="item?.id?.kind === 'youtube#video'" -->
                                <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 32px; bottom: 12px;">
                                    <div class="relative">
                                        <div v-if="youtubeId === item.id.videoId" class="checkmark-for-choosen">
                                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="50%" height="50%" fill="none" viewBox="0 0 24 24">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 11.917 9.724 16.5 19 7.5"/>
                                            </svg>
                                        </div>

                                        <img :src="item.snippet.thumbnails.high.url" alt="Thumbnail" style="width: 100%;">
                                    </div>

                                    <div class="youtube-video-infos">
                                        <h3>{{ item.snippet.title }}</h3>
                                        <p>{{ item.snippet.description }}</p>
                                        <p>{{ calculateTimeDifference(item.snippet.publishedAt) }}</p>
                                        <hr>
                                        <p>{{item}}</p>
                                        <!-- <p>{{ item.snippet.thumbnails.high.url }}</p> -->
                                        <!-- <span>youtubeId === item.snippet.id?: {{youtubeId}} === {{item.snippet}}</span> -->
                                        
                                        <!-- <p>{{ item }}</p> -->
                                    </div>
                                    <!-- <p>{{ item.id.videoId }}</p> -->
                                </div>
                            </li>
                            <li 
                                v-if="isShowAllVideosActive && youtubeApiData && filteredYoutubeApiItems.length === 0 || searchChars && filteredYoutubeApiItems.length === 0" 
                                class="video-item"
                                style="padding-left: 20px;"
                            >
                                <p>Keine Videos gefunden</p>
                            </li>
                        </ul>
                    </div>

                    <label for="youtube_video_url">
                        Youtube-Video-ID  | Bitte nur eingeben, wenn Du weißt was Du machst ;)
                        <br>Die ID findest Du in der URL des Videos zwischen "v=" und "&".  
                    </label>
                    <br>
                    <input 
                        type="text" 
                        id="youtube_video_url" 
                        name="youtube_video_url" 
                        v-model="youtubeId"
                        style="width: 100%; color: #ccc;"
                    >
                </div>

            </div>

            <script type="module">
                const { createApp, ref, onMounted, computed } = Vue

                const app = createApp({
                    setup() {
                        const message = ref('Hello Vue 3!')
                        const count = ref(0)
                        const inc = () => count.value++
                        const dec = () => count.value--

                        const selectedChannelIds = ref([]); // Array statt einzelner String

                        // Funktion zum Umschalten der Kanal-Auswahl
                        function toggleChannelSelection(channelId) {
                            if (selectedChannelIds.value.includes(channelId)) {
                                // Entferne den Kanal, wenn er schon ausgewählt ist
                                selectedChannelIds.value = selectedChannelIds.value.filter(id => id !== channelId);
                            } else {
                                // Füge den Kanal hinzu
                                selectedChannelIds.value.push(channelId);
                            }
                        }

                        function toggleShowAllChannels() {
                            if (isShowAllVideosActive.value) {
                                // Deaktiviere "alle Channels anzeigen"
                                isShowAllVideosActive.value = false;
                            } else {
                                // Aktiviere "alle Channels anzeigen" und leere die Kanal-Auswahl
                                selectedChannelIds.value = [];
                                isShowAllVideosActive.value = true;
                            }
                        }

                        const youtube_api_key = '<?php echo esc_attr($youtube_api_key)?>'
                        const youtube_channels = <?php echo json_encode($youtube_channels); ?>;

                        const youtubeId = ref('<?php echo esc_attr($youtube_video_url); ?>')

                        const isShowAllVideosActive = ref(false)

                        async function fetchYoutubeData(apiKey, channelId) {
                            let allResults = [];
                            let nextPageToken = '';
                            const baseUrl = `https://youtube.googleapis.com/youtube/v3/search?part=snippet&channelId=${channelId}&order=date&maxResults=50&key=${apiKey}`;
                            let aggregatedResponse = {};

                            do {
                                const url = nextPageToken ? `${baseUrl}&pageToken=${nextPageToken}` : baseUrl;
                                try {
                                    const response = await fetch(url);
                                    if (!response.ok) {
                                        throw new Error('Network response was not ok: ' + response.status);
                                    }
                                    const data = await response.json();
                                    // Bei der ersten Anfrage die Basis-Daten speichern
                                    if (!nextPageToken) {
                                        aggregatedResponse = data;
                                    }
                                    allResults = allResults.concat(data.items);
                                    nextPageToken = data.nextPageToken;
                                } catch (error) {
                                    console.error('Error fetching data for channel ' + channelId + ':', error);
                                    return null;
                                }
                            } while (nextPageToken);

                            // Aggregierte Struktur zusammenstellen
                            aggregatedResponse.items = allResults;
                            aggregatedResponse.pageInfo.totalResults = allResults.length;
                            aggregatedResponse.pageInfo.resultsPerPage = 50; // oder wie benötigt
                            return aggregatedResponse;
                        }


                        const apiKey = 'AIzaSyAaJbcuUGS9tbyL1E5PpklS5POnCKDZrEA';
                        const youtubeApiData = ref({})

                        const fetchYoutubeDataFromDatabase = () => {
                            console.log('fetchYoutubeDataFromDatabase called')
                            axios.post(ajax_object.ajax_url, new URLSearchParams({
                                action: 'get_youtube_data'
                            }))
                            .then(response => {
                                if (response.data.success) {
                                    youtubeApiData.value = response.data.data;
                                    console.log('Data fetched successfully', youtubeApiData.value);
                                } else {
                                    console.error('Data fetching failed', response.data);
                                }
                            })
                            .catch(error => {
                                console.error('Error fetching data:', error);
                            });
                        };
                        fetchYoutubeDataFromDatabase()


                        const playerWrapper = document.getElementById('player-wrapper');
                        console.log('playerWrapper: ', playerWrapper);

                        const createNewYoutubeIframe = (newYoutubeId) => {
                            console.log('createNewYoutubeIframe called: ', newYoutubeId);
                            playerWrapper.innerHTML = '<div id="player"></div>';
                            console.log('playerWrapper: ', playerWrapper);
                            
                            player = new YT.Player('player', {
                                height: 'auto',
                                width: '100%',
                                videoId: newYoutubeId,
                                playerVars: {
                                    'playsinline': 1,
                                    'controls': 1,
                                    // 'mute': 1,
                                },
                                events: {
                                    // 'onReady': onPlayerReady
                                }
                            });
                        }

                        const destroyYoutubeIframe = () => {
                            if (player && typeof player.destroy === 'function') {
                                player.destroy();
                                console.log('YouTube player destroyed.');
                            } else {
                                console.log('No player to destroy.');
                            }
                        };

                        
                        const manualSyncData = async () => {
                            let allVideos = [];
                            for (const channel of youtube_channels) {
                                console.log('Fetching data for channel:', channel.id);
                                const data = await fetchYoutubeData(youtube_api_key, channel.id);
                                if (data && data.items) {
                                    allVideos = allVideos.concat(data.items);
                                }
                            }
                            const aggregatedResponse = {
                                kind: "youtube#searchListResponse",
                                etag: "aggregated_etag",
                                regionCode: "DE",
                                pageInfo: {
                                    totalResults: allVideos.length,
                                    resultsPerPage: 50
                                },
                                items: allVideos
                            };
                            youtubeApiData.value = aggregatedResponse;
                            console.log('Aggregated YouTube Data:', youtubeApiData.value);
                            sendData();
                        }

                        const searchChars = ref('');

    
                        const filteredYoutubeApiItems = computed(() => {
                            if (!youtubeApiData.value || !youtubeApiData.value.items) return [];
                            
                            return youtubeApiData.value.items
                                .filter(item => {
                                // Nur Videos berücksichtigen
                                if (item?.id?.kind !== 'youtube#video') return false;
                                
                                // Suchfilter: Falls ein Suchstring eingegeben wurde, muss der Titel diesen enthalten.
                                if (searchChars.value && 
                                    !item.snippet.title.toLowerCase().includes(searchChars.value.toLowerCase())) {
                                    return false;
                                }
                                
                                // Kanalfilter: Falls isShowAllVideosActive nicht gesetzt ist und eine Auswahl vorliegt,
                                // wird nur angezeigt, wenn das Video von einem der ausgewählten Kanäle stammt.
                                if (!isShowAllVideosActive.value && 
                                    selectedChannelIds.value.length > 0 &&
                                    !selectedChannelIds.value.includes(item.snippet.channelId)) {
                                    return false;
                                }
                                
                                // Wenn weder ein Suchstring noch eine Kanal-Auswahl vorhanden sind und
                                // isShowAllVideosActive nicht aktiviert ist, gib nichts zurück.
                                if (!searchChars.value && selectedChannelIds.value.length === 0 && !isShowAllVideosActive.value) {
                                    return false;
                                }
                                
                                return true;
                            })
                            // Sortiere nach Veröffentlichungsdatum (neueste zuerst)
                            .sort((a, b) => new Date(b.snippet.publishedAt) - new Date(a.snippet.publishedAt));
                        });





                        const choosenVideo = ref();

                        // const youtubeVideoIdInput = ref('');

                        const useVideo = (video) => {
                            if(youtubeId.value === video.id.videoId) {
                                choosenVideo.value = {
                                    title: '',
                                    description: '',
                                    videoId: ''
                                }

                                youtubeId.value = '';

                                destroyYoutubeIframe();
                                return
                            }

                            choosenVideo.value = {
                                title: video.snippet.title,
                                description: video.snippet.description,
                                videoId: video.id.videoId
                            }

                            youtubeId.value = video.id.videoId

                            createNewYoutubeIframe(video.id.videoId);
                        }

                        function calculateTimeDifference(dateString) {
                            const givenDate = new Date(dateString);
                            const currentDate = new Date();

                            const timeDifference = currentDate - givenDate;
                            const daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));

                            if (daysDifference > 365) {
                                const yearsDifference = Math.floor(daysDifference / 365);
                                return `${yearsDifference} years ago`;
                            } else {
                                return `${daysDifference} days ago`;
                            }
                        }

                        const sendData = () => {
                            axios.post(ajax_object.ajax_url, new URLSearchParams({
                                action: 'insert_youtube_data',
                                data: JSON.stringify(youtubeApiData.value)
                            }))
                            .then(response => {
                                if (response.data.success) {
                                    console.log('Data inserted successfully');
                                } else {
                                    console.log('Data insertion failed', response.data);
                                }
                            })
                            .catch(error => {
                                console.error('Error inserting data:', error);
                            });
                        };

                        return {
                            message,
                            count,
                            inc,
                            dec,

                            // UI stuff 
                            isShowAllVideosActive,
                            calculateTimeDifference,

                            // youtube:
                            toggleChannelSelection,
                            toggleShowAllChannels,
                            youtube_api_key,
                            youtube_channels,
                            selectedChannelIds,
                            // "youtubeId" is the choosen video ID
                            youtubeId,

                            // youtubeApiData
                            youtubeApiData,
                            manualSyncData,

                            // filtered youtube api data
                            searchChars,
                            filteredYoutubeApiItems,

                            choosenVideo, 
                            useVideo,
                        }
                    }
                })

                app.mount('#app')
            </script>
        </div>
        <!-- End meta Content -->

        <script>
            const spin = document.querySelector("#spin");
            const metaContent = document.querySelector("#meta-content");

            window.addEventListener('load', function() {
                spin.classList.add("hidden");
                metaContent.classList.remove("hidden");
            })
        </script>

    </div>
    <?php
}


function save_video_metabox_data($post_id) {
    // Überprüfe, ob unser Formular überhaupt abgesendet wurde.
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern von use_youtube_frame (Checkbox)
    if (array_key_exists('use_youtube_frame', $_POST)) {
        update_post_meta($post_id, 'use_youtube_frame', '1');
        error_log('use_youtube_frame saved: 1');
    } else {
        update_post_meta($post_id, 'use_youtube_frame', '0');
        error_log('use_youtube_frame saved: 0');
    }

    // Speichern der ausgewählten Video-ID
    if (array_key_exists('selected_video_id', $_POST)) {
        $selected_video_id = sanitize_text_field($_POST['selected_video_id']);
        update_post_meta($post_id, 'selected_video_id', $selected_video_id);
        error_log('selected_video_id saved: ' . $selected_video_id);
    } else {
        error_log('selected_video_id not found in $_POST');
    }

    // Speichern der YouTube-Video-URL
    if (array_key_exists('youtube_video_url', $_POST)) {
        $youtube_video_url = sanitize_text_field($_POST['youtube_video_url']);
        update_post_meta($post_id, 'youtube_video_url', $youtube_video_url);
        error_log('youtube_video_url saved: ' . $youtube_video_url);
    } else {
        error_log('youtube_video_url not found in $_POST');
    }

    // Speichern der Hero Headline
    if (array_key_exists('video_headline', $_POST)) {
        $video_headline = sanitize_text_field($_POST['video_headline']);
        update_post_meta($post_id, 'video_headline', $video_headline);
        error_log('video_headline saved: ' . $video_headline);
    } else {
        error_log('video_headline not found in $_POST');
    }

    // Falls du noch weitere Felder hast, z.B. selected_thumbnail_id:
    if (array_key_exists('selected_thumbnail_id', $_POST)) {
        $selected_thumbnail_id = sanitize_text_field($_POST['selected_thumbnail_id']);
        update_post_meta($post_id, 'selected_thumbnail_id', $selected_thumbnail_id);
        error_log('selected_thumbnail_id saved: ' . $selected_thumbnail_id);
    } else {
        error_log('selected_thumbnail_id not found in $_POST');
    }
}

// Verwende den spezifischen Hook für deinen CPT "videothek"
add_action('save_post_videothek', 'save_video_metabox_data');




/**
 * FRONTEND
 */
// Inject Code in Single Post
require_once MY_PLUGIN_ROOT . 'includes/single-post-injections/inject_video_hero_section.php';

/** load single pages for videothek */
function videothek_single_template($single_template) {
    if (is_singular('videothek')) {
        $plugin_template = MY_PLUGIN_ROOT . 'templates/single-videothek.php';
        if (file_exists($plugin_template)) {
            return $plugin_template;
        }
    }
    return $single_template;
}
add_filter('single_template', 'videothek_single_template');

/** load archive pages for videothek */
function videothek_archive_template($archive_template) {
    if (is_post_type_archive('videothek')) {
        $plugin_template = MY_PLUGIN_ROOT . 'templates/archive-videothek.php';
        if (file_exists($plugin_template)) {
            return $plugin_template;
        }
    }
    return $archive_template;
}
add_filter('archive_template', 'videothek_archive_template');

/* 
 * TESTING Youtube CHannel Search 
 */
// Ersetze diese Werte durch deine eigenen
// const apiKey = 'AIzaSyAaJbcuUGS9tbyL1E5PpklS5POnCKDZrEA';
// const forUsername = 'GoogleDevelopers'; // Beispiel: Kanalname

// fetch(`https://www.googleapis.com/youtube/v3/channels?part=id&forUsername=${forUsername}&key=${apiKey}`)
//   .then(response => {
//     if (!response.ok) {
//       throw new Error("HTTP-Fehler: " + response.status);
//     }
//     return response.json();
//   })
//   .then(data => {
//     if (data.items && data.items.length > 0) {
//       console.log("Channel ID:", data.items[0].id);
//     } else {
//       console.log("Kein Kanal gefunden für den Benutzernamen:", forUsername);
//     }
//   })
//   .catch(error => console.error("Fehler:", error));