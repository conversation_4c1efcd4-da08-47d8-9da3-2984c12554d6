<?php
/**
 * Registriert CPT social wall
 */

// Verhindert direkten Zugriff
if (!defined('ABSPATH')) {
    exit;
}

function register_social_wall() {
    register_post_type('social_wall', [
        'labels' => [
            'name' => __('Social Wall', 'textdomain'),
            'singular_name' => __('Social Wall', 'textdomain')
        ],
        'public' => true,
        'has_archive' => true,
        'rewrite' => array('slug' => 'social-wall'),
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'show_in_rest' => true,
        // 'show_in_menu' => false 
        // 'show_in_menu' => 'vecura-mediahub-settings',
        // 'show_in_admin_bar' => true,
    ]);
}
add_action('init', 'register_social_wall');


// Inject Code in Single Post  
require_once MY_PLUGIN_ROOT . 'includes/single-post-injections/inject_social_wall_hero_section.php';