<?php
/**
 * Registriert CPT social wall
 */

// Verhindert direkten Zugriff
if (!defined('ABSPATH')) {
    exit;
}

function register_news() {
    register_post_type('news', [
        'labels' => [
            'name' => __('News', 'textdomain'),
            'singular_name' => __('News', 'textdomain')
        ],
        'public' => true,
        'has_archive' => true,
        'rewrite' => array('slug' => 'news'),
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'show_in_rest' => true,
        // 'show_in_menu' => false 
        // 'show_in_menu' => 'vecura-mediahub-settings',
        // 'show_in_admin_bar' => true
    ]);
}
add_action('init', 'register_news');