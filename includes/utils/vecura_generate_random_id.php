<?php
/**
 * Erzeugt eine zufällige ID aus alphanumerischen Zeichen.
 *
 * @param int $length Die Länge der zu erzeugenden ID. Standard ist 10.
 * @return string Die zufällig generierte ID.
 */
function vecura_generate_random_id($length = 10) {
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomId = '';
    for ($i = 0; $i < $length; $i++) {
        $randomId .= $characters[random_int(0, $charactersLength - 1)];
    }
    return $randomId;
}