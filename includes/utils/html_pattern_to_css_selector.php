<?php
/**
 * Wandelt ein HTML-Tag-Pattern in einen CSS-Selektor um.
 *
 * @param string $pattern Das HTML-Pattern, z. B. '<main id="content" class="site-content"'
 * @return string Der resultierende CSS-Selektor, z. B. 'main#content.site-content'
 */
function html_pattern_to_css_selector($pattern) {
    // Mit Regex Tag, id und class extrahieren
    if (preg_match('/^<(\w+)(?:\s+[^>]*?)?(?:id="([^"]+)")?(?:\s+[^>]*?)?(?:class="([^"]+)")?/', $pattern, $matches)) {
        // Tag-Name extrahieren (z. B. "main")
        $tag = isset($matches[1]) ? $matches[1] : '';
        // Falls eine ID vorhanden ist, diese mit "#" voranstellen
        $id  = isset($matches[2]) && $matches[2] ? '#' . $matches[2] : '';
        
        $classes = '';
        if (isset($matches[3]) && $matches[3]) {
            // Falls Klassen vorhanden sind, diese trennen und jeweils mit "." versehen
            $classArray = preg_split('/\s+/', trim($matches[3]));
            foreach ($classArray as $class) {
                $classes .= '.' . $class;
            }
        }
        // Zusammensetzen des Selektors
        $selector = $tag . $id . $classes;
        return $selector;
    }
    // Falls kein passendes Pattern gefunden wird, zurückgeben
    return '';
}

// // Beispielaufruf:
// $pattern = '<main id="content" class="site-content"';
// $selector = html_pattern_to_css_selector($pattern);
// echo 'CSS Selector: ' . $selector;
