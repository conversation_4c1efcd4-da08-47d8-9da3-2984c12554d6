<?php
// Verhindert direkten Zugriff
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Function zum erkennen des Page Builders
 * 
 * In WordPress gibt es mehrere Page Builder wie WPBakery, Elementor, Divi, Gutenberg usw.
 * Jeder Page Builder hat seine eigene Art, den Inhalt im DOM (HTML-Struktur) auszugeben.
 
 * 👉 Das bedeutet:
 
 * Der HTML-Aufbau ist je nach Page Builder anders.
 * Dein Code muss wissen, wo er Inhalte einfügen kann.
 * Sonst kann es passieren, dass dein Hero-Bereich an der falschen Stelle oder gar nicht erscheint.
 * 
 */
function detect_active_page_builder() {
    static $active_builder = null; // Speichert den Wert nach dem ersten Aufruf

    if ($active_builder !== null) {
        return $active_builder; // Falls schon bestimmt, sofort zurückgeben
    }

    if (class_exists('WPBakeryVisualComposerAbstract') || defined('WPB_VC_VERSION')) {
        $active_builder = 'wpbakery';
    } elseif (did_action('elementor/loaded')) {
        $active_builder = 'elementor';
    } elseif (class_exists('ET_Builder_Module')) {
        $active_builder = 'divi';
    } elseif (function_exists('use_block_editor_for_post') && use_block_editor_for_post(get_the_ID())) {
        $active_builder = 'gutenberg';
    } elseif (class_exists('CustomPageBuilder') || defined('CUSTOM_PAGE_BUILDER')) {
        $active_builder = 'custom';
    } else {
        $active_builder = 'default'; // Kein bekannter Page Builder aktiv
    }

    return $active_builder;
}

// function detect_active_page_builder() {
//     // WPBakery Page Builder (ehemals Visual Composer)
//     if (class_exists('WPBakeryVisualComposerAbstract') || defined('WPB_VC_VERSION')) {
//         return 'wpbakery';
//     }

//     // Elementor
//     if (did_action('elementor/loaded')) {
//         return 'elementor';
//     }

//     // Divi Builder
//     if (class_exists('ET_Builder_Module')) {
//         return 'divi';
//     }

//     // Gutenberg (Standard WordPress Editor)
//     if (function_exists('use_block_editor_for_post') && use_block_editor_for_post(get_the_ID())) {
//         return 'gutenberg';
//     }

//     // Kreativdenker Page Builder
//     if (class_exists('CustomPageBuilder') || defined('CUSTOM_PAGE_BUILDER')) {
//         return 'custom';
//     }

//     // Kein bekannter Page Builder aktiv
//     return 'default';
// }


// global $active_page_builder;
// $active_page_builder = detect_active_page_builder();