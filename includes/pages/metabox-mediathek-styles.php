<?php
/**
 * Datei: metabox-mediathek-styles.php
 * Zweck: Fügt eine Meta Box zu Seiten hinzu, um "Use Mediathek Styles" zu aktivieren, und wendet diese Styles dann im Frontend an.
 */

/** 
 * Fügt eine Meta Box zu normalen Seiten hinzu, in der der Nutzer "Use Mediathek Styles" auswählen kann.
 */
function add_mediathek_styles_metabox() {
    add_meta_box(
        'mediastyles_metabox',              // ID der Meta Box
        __('Mediathek Styles', 'textdomain'), // Titel der Meta Box
        'render_mediathek_styles_metabox',   // Callback zum Rendern der Box
        'page',                              // Post Type (Seiten)
        'side',                              // Kontext (Seitenleiste)
        'default'                            // Priorität
    );
}
add_action('add_meta_boxes', 'add_mediathek_styles_metabox');

/**
 * Rendert die Meta Box.
 *
 * @param WP_Post $post Der aktuelle Post.
 */
function render_mediathek_styles_metabox($post) {
    // Sicherheits-Nonce
    wp_nonce_field('save_mediathek_styles_metabox', 'mediastyles_metabox_nonce');
    // Aktuellen Wert abrufen (Standard: 0)
    $use_styles = get_post_meta($post->ID, 'use_mediathek_styles', true);
    ?>
    <label for="use_mediathek_styles">
        <input type="checkbox" name="use_mediathek_styles" id="use_mediathek_styles" value="1" <?php checked($use_styles, '1'); ?>>
        <?php _e('Use Mediathek Styles', 'textdomain'); ?>
    </label>
    <?php
}

/**
 * Speichert die Meta Box-Daten.
 *
 * @param int $post_id Die ID des aktuellen Posts.
 */
function save_mediathek_styles_metabox($post_id) {
    // Sicherheitsprüfungen
    if (!isset($_POST['mediastyles_metabox_nonce']) || !wp_verify_nonce($_POST['mediastyles_metabox_nonce'], 'save_mediathek_styles_metabox')) {
        return;
    }
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    if (!current_user_can('edit_page', $post_id)) {
        return;
    }
    // Checkbox: Wenn gesetzt, speichern wir "1", ansonsten "0"
    $value = isset($_POST['use_mediathek_styles']) ? '1' : '0';
    update_post_meta($post_id, 'use_mediathek_styles', $value);
}
add_action('save_post', 'save_mediathek_styles_metabox');


// Definierte Patterns, nach denen im Seiteninhalt gesucht werden soll:
$patterns = [
    '<div class="container-wrap"', // z. B. WP-Backery
    '<main id="content" class="site-content"',
    '<main id="content"',
    '<main id="main"',
    '<main class="',
    '<main>',
    '<div id="main"',
    '<div id="content"'
];

/**
 * Ermittelt anhand des Seiteninhalts einen CSS-Selektor.
 *
 * @return string Der ermittelte CSS-Selektor oder ein Default-Selektor.
 */
/**
 * Verbesserte Funktion zur Ermittlung des CSS-Selektors
 * Berücksichtigt Shortcodes und Page Builder
 */
function get_used_page_selector() {
    global $post;
    
    if (!$post || !isset($post->post_content)) {
        return '.site-content'; // Fallback
    }
    
    $content = $post->post_content;
    
    // 1. Prüfe auf WP Bakery Shortcodes (diese werden später zu container-wrap)
    if (strpos($content, '[vc_row') !== false || 
        strpos($content, '[vc_column') !== false ||
        strpos($content, 'vc_row') !== false) {
        return '.container-wrap';
    }
    
    // 2. Prüfe auf Elementor
    if (strpos($content, 'elementor') !== false ||
        get_post_meta($post->ID, '_elementor_edit_mode', true)) {
        return '.elementor';
    }
    
    // 3. Prüfe auf Gutenberg Blocks
    if (has_blocks($content)) {
        return '.wp-block-group';
    }
    
    // 4. Prüfe auf bereits gerenderten HTML-Code (falls vorhanden)
    $html_patterns = [
        '<div class="container-wrap"' => '.container-wrap',
        '<main id="content" class="site-content"' => '#content.site-content', 
        '<main id="content"' => '#content',
        '<main id="main"' => '#main',
        '<main class="' => 'main',
        '<main>' => 'main',
        '<div id="main"' => '#main',
        '<div id="content"' => '#content'
    ];
    
    foreach ($html_patterns as $pattern => $selector) {
        if (strpos($content, $pattern) !== false) {
            return html_pattern_to_css_selector($pattern);
        }
    }
    
    // 5. Fallback-Selektor
    return '.site-content';
}

/**
 * Wendet die Mediathek-Styles auf Seiten an, wenn in der Meta Box "Use Mediathek Styles" aktiviert ist.
 */
function apply_mediathek_styles_on_page() {
    if (is_singular('page')) {
        global $post;
        $use_mediathek_styles = get_post_meta($post->ID, 'use_mediathek_styles', true);
        if ('1' === $use_mediathek_styles) {
            // Globale Optionen abrufen:
            $background_color = get_option('vecura_mediathek_background_color', '#000000');
            $text_color = get_option('vecura_mediathek_text_color', '#000000');
            // Dynamisch ermittelter CSS-Selektor
            $selector = get_used_page_selector();
            echo '<style>
                body, ' . esc_attr($selector) . ' {
                    background-color: ' . esc_attr($background_color) . ' !important;
                    color: ' . esc_attr($text_color) . ';
                }
            </style>';
        }
    }
}
add_action('wp_head', 'apply_mediathek_styles_on_page');
