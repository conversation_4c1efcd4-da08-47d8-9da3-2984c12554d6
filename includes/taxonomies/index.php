<?php
// Verhindert direkten Zugriff
if (!defined('ABSPATH')) {
    exit;
}

// Lade alle Taxonomie-Dateien automatisch
// foreach (glob(plugin_dir_path(__FILE__) . '*.php') as $file) {
//     if (basename($file) !== 'index.php') {
//         require_once $file;
//     }
// }



/*
// Allgemeine Funktion zur Anzeige und Verwaltung von Taxonomien
// Allgemeine Funktion zum Verwalten der Taxonomien für jeden benutzerdefinierten Post-Typ
function manage_taxonomies_page($post_type) {
    // Dynamischer Option-Name basierend auf dem Post-Typ
    $taxonomy_option_name = 'custom_taxonomies_' . $post_type;

    // Verarbeitung des Formulars für das Erstellen neuer Taxonomien
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && !empty($_POST['taxonomy_name'])) {
        $taxonomy_name = sanitize_text_field($_POST['taxonomy_name']);
        
        $existing_taxonomies = get_option($taxonomy_option_name, array());
        if (!in_array($taxonomy_name, $existing_taxonomies)) {
            $existing_taxonomies[] = $taxonomy_name;
            update_option($taxonomy_option_name, $existing_taxonomies);
        }

        echo '<div class="notice notice-success is-dismissible"><p>' . __('Taxonomie erfolgreich erstellt!', 'textdomain') . '</p></div>';
    }

    // HTML-Formular für das Erstellen einer neuen Taxonomie
    echo '<div class="wrap">';
    echo '<h1>' . __('Taxonomie für ' . ucfirst($post_type) . ' erstellen', 'textdomain') . '</h1>';
    echo '<form method="POST">';
    echo '<label for="taxonomy_name">' . __('Taxonomie-Name', 'textdomain') . ':</label>';
    echo '<input type="text" name="taxonomy_name" id="taxonomy_name" required>';
    echo '<br><br>';
    echo '<button type="submit" class="button button-primary">' . __('Taxonomie erstellen', 'textdomain') . '</button>';
    echo '</form>';
    echo '</div>';

    // Vorhandene Taxonomien anzeigen
    display_taxonomies($taxonomy_option_name);

    // Löschen von Taxonomien ermöglichen
    delete_taxonomy($taxonomy_option_name);
}

// Seiten für Social Wall, Videothek und News Taxonomien
function manage_social_wall_taxonomies_page() { manage_taxonomies_page('social_wall'); }
function manage_videothek_taxonomies_page() { manage_taxonomies_page('videothek'); }
function manage_news_taxonomies_page() { manage_taxonomies_page('news'); }


// Funktion zur Anzeige von Taxonomien und Hinzufügen des Lösch-Links
// Funktion zur Anzeige von Taxonomien und Hinzufügen des Lösch-Links
// In der display_taxonomies Funktion
function display_taxonomies($taxonomy_option_name) {
    $existing_taxonomies = get_option($taxonomy_option_name, array());
    $post_type = str_replace('custom_taxonomies_', '', $taxonomy_option_name);  // Extrahiere den Post-Typ

    if (!empty($existing_taxonomies)) {
        echo '<h2>' . __('Bereits erstellte Taxonomien', 'textdomain') . '</h2>';
        echo '<ul>';
        foreach ($existing_taxonomies as $taxonomy) {
            // Füge einen JS-Event-Handler hinzu
            echo '<li>' . esc_html($taxonomy) . ' <a href="?post_type=' . esc_attr($post_type) . '&delete_taxonomy=' . esc_attr($taxonomy) . '" onclick="return confirm(\'Möchten Sie diese Taxonomie wirklich löschen?\')">Löschen</a></li>';
        }
        echo '</ul>';
    } else {
        echo '<p>' . __('Keine Taxonomien gefunden.', 'textdomain') . '</p>';
    }
}


// Sicherstellen, dass die delete_taxonomy Funktion bei jedem Laden des Admin-Bereichs ausgeführt wird
add_action('admin_init', 'check_for_taxonomy_deletion');

// Funktion zur Prüfung und zum Löschen einer Taxonomie, wenn der Parameter vorhanden ist
function check_for_taxonomy_deletion() {
    // Prüfe, ob der Parameter "delete_taxonomy" gesetzt ist und führe die Löschfunktion aus
    if (isset($_GET['delete_taxonomy']) && isset($_GET['post_type'])) {
        $post_type = sanitize_text_field($_GET['post_type']);
        $taxonomy_option_name = 'custom_taxonomies_' . $post_type;

        // Führe die Löschfunktion aus
        delete_taxonomy($taxonomy_option_name);
    }
}

// Funktion zum Löschen einer Taxonomie
function delete_taxonomy($taxonomy_option_name) {
    // Prüfe, ob der Parameter "delete_taxonomy" gesetzt ist
    if (isset($_GET['delete_taxonomy'])) {
        $taxonomy_to_delete = sanitize_text_field($_GET['delete_taxonomy']);
        
        // Rufe die Funktion zum Löschen der Taxonomie und ihrer Begriffe auf
        delete_taxonomy_and_terms($taxonomy_option_name, $taxonomy_to_delete);

        // Erfolgreiche Löschungsnachricht anzeigen
        echo '<div class="notice notice-success is-dismissible"><p>' . __('Taxonomie und zugehörige Begriffe gelöscht.', 'textdomain') . '</p></div>';

        // Weiterleitung, um den Parameter aus der URL zu entfernen und die Seite sauber neu zu laden
        $redirect_url = admin_url('edit.php?post_type=' . $post_type);
        wp_safe_redirect($redirect_url);
        // exit; // Beende das Skript, um sicherzustellen, dass keine weiteren Aktionen ausgeführt werden
    }
}

// Funktion zum Löschen einer Taxonomie und ihrer Begriffe
function delete_taxonomy_and_terms($taxonomy_option_name, $taxonomy_to_delete) {
    // Lösche die Begriffe, die der Taxonomie zugeordnet sind
    $terms = get_terms(array(
        'taxonomy' => $taxonomy_to_delete,
        'hide_empty' => false,
    ));

    // Wenn Begriffe gefunden wurden, diese löschen
    if (!empty($terms) && !is_wp_error($terms)) {
        foreach ($terms as $term) {
            wp_delete_term($term->term_id, $taxonomy_to_delete);
        }
    }

    // Entferne die Taxonomie aus den gespeicherten Optionen
    $existing_taxonomies = get_option($taxonomy_option_name, array());
    if (($key = array_search($taxonomy_to_delete, $existing_taxonomies)) !== false) {
        unset($existing_taxonomies[$key]);
        update_option($taxonomy_option_name, $existing_taxonomies);
    }

    // Taxonomie aus der Registrierung entfernen (optional, wenn sie dynamisch registriert wurde)
    unregister_taxonomy($taxonomy_to_delete);
}



// Admin-Notice nach dem Löschen anzeigen
function show_taxonomy_delete_notice() {
    // Überprüfen, ob eine Nachricht im Transient gespeichert wurde
    if ($message = get_transient('delete_taxonomy_notice')) {
        // Nachricht anzeigen
        echo '<div class="notice notice-success is-dismissible"><p>' . esc_html($message) . '</p></div>';
        // Lösche das Transient, damit die Nachricht nicht erneut angezeigt wird
        delete_transient('delete_taxonomy_notice');
    }
}
add_action('admin_notices', 'show_taxonomy_delete_notice');



// Menüpunkte für benutzerdefinierte Taxonomien hinzufügen
function add_taxonomy_menu_pages() {
    // Untermenüpunkt für Social Wall Taxonomien
    add_submenu_page(
        'edit.php?post_type=social_wall',
        __('Taxonomien verwalten', 'textdomain'),
        __('Taxonomien verwalten', 'textdomain'),
        'manage_options',
        'manage_social_wall_taxonomies',
        'manage_social_wall_taxonomies_page'
    );

    // Untermenüpunkt für Videothek Taxonomien
    add_submenu_page(
        'edit.php?post_type=videothek',
        __('Taxonomien verwalten', 'textdomain'),
        __('Taxonomien verwalten', 'textdomain'),
        'manage_options',
        'manage_videothek_taxonomies',
        'manage_videothek_taxonomies_page'
    );

    // Untermenüpunkt für News Taxonomien
    add_submenu_page(
        'edit.php?post_type=news',
        __('Taxonomien verwalten', 'textdomain'),
        __('Taxonomien verwalten', 'textdomain'),
        'manage_options',
        'manage_news_taxonomies',
        'manage_news_taxonomies_page'
    );
}
add_action('admin_menu', 'add_taxonomy_menu_pages');

// Registrierung der benutzerdefinierten Taxonomien nach Erstellung
function register_custom_taxonomies($post_type) {
    $taxonomy_option_name = 'custom_taxonomies_' . $post_type;
    $taxonomies = get_option($taxonomy_option_name, array());

    foreach ($taxonomies as $taxonomy) {
        register_taxonomy($taxonomy, $post_type, array(
            'labels' => array(
                'name' => $taxonomy,
                'singular_name' => $taxonomy,
            ),
            'public' => true,
            'hierarchical' => true,
            'show_ui' => true,
            'show_in_rest' => true,
        ));
    }
}

add_action('after_setup_theme', function() { register_custom_taxonomies('social_wall'); });
add_action('after_setup_theme', function() { register_custom_taxonomies('videothek'); });
add_action('after_setup_theme', function() { register_custom_taxonomies('news'); });


//////////////////////////////
// Add SVG Field in Kategories
//////////////////////////////
// Fügt das SVG-Feld zum Formular für neue Terme hinzu
function add_term_svg_field($taxonomy) {
    ?>
    <div class="form-field">
        <label for="term_svg"><?php _e('SVG Code', 'textdomain'); ?></label>
        <textarea name="term_svg" id="term_svg" rows="5" class="large-text"></textarea>
        <p class="description"><?php _e('Fügen Sie hier den SVG-Code für diese Kategorie ein.', 'textdomain'); ?></p>
    </div>
    <?php
}

// Fügt das SVG-Feld zum Bearbeiten-Formular hinzu
function edit_term_svg_field($term, $taxonomy) {
    $svg_code = get_term_meta($term->term_id, 'term_svg', true);
    ?>
    <tr class="form-field">
        <th scope="row">
            <label for="term_svg"><?php _e('SVG Code', 'textdomain'); ?></label>
        </th>
        <td>
            <textarea name="term_svg" id="term_svg" rows="5" class="large-text"><?php echo esc_textarea($svg_code); ?></textarea>
            <p class="description"><?php _e('Fügen Sie hier den SVG-Code für diese Kategorie ein.', 'textdomain'); ?></p>
            <?php if (!empty($svg_code)) : ?>
            <div class="svg-preview">
                <h4><?php _e('SVG Vorschau:', 'textdomain'); ?></h4>
                <?php echo wp_kses_post($svg_code); ?>
            </div>
            <?php endif; ?>
        </td>
    </tr>
    <?php
}

// Speichert das SVG-Feld
// Funktion zum Speichern des SVG-Feldes
function save_term_svg_field($term_id) {
    if (isset($_POST['term_svg'])) {
        $svg_code = $_POST['term_svg'];
        // Erlaubte SVG-Tags und Attribute definieren
        $allowed_html = array(
            'svg' => array(
                'class' => true,
                'aria-hidden' => true,
                'aria-labelledby' => true,
                'role' => true,
                'xmlns' => true,
                'width' => true,
                'height' => true,
                'viewbox' => true,
                'viewBox' => true // Beide Schreibweisen erlauben
            ),
            'path' => array(
                'd' => true,
                'fill' => true
            ),
            'style' => array(),
            'g' => array(),
            'title' => array()
        );
        
        // SVG säubern und speichern
        $clean_svg = wp_kses($svg_code, $allowed_html);
        update_term_meta($term_id, 'term_svg', $clean_svg);
    }
}

// Fügt die Hooks für alle benutzerdefinierten Taxonomien hinzu
function register_term_svg_fields() {
    // Array der Post-Typen
    $post_types = ['social_wall', 'videothek', 'news'];
    
    foreach ($post_types as $post_type) {
        // Hole alle Taxonomien für diesen Post-Typ
        $taxonomy_option_name = 'custom_taxonomies_' . $post_type;
        $taxonomies = get_option($taxonomy_option_name, array());
        
        foreach ($taxonomies as $taxonomy) {
            // Hooks für das Hinzufügen neuer Terme
            add_action($taxonomy . '_add_form_fields', 'add_term_svg_field');
            // Hooks für das Bearbeiten bestehender Terme
            add_action($taxonomy . '_edit_form_fields', 'edit_term_svg_field', 10, 2);
            // Hooks für das Speichern
            add_action('created_' . $taxonomy, 'save_term_svg_field');
            add_action('edited_' . $taxonomy, 'save_term_svg_field');
        }
    }
}
add_action('admin_init', 'register_term_svg_fields');
*/


/**
 * Erstellt und verwaltet Taxonomien für benutzerdefinierte Post-Typen.
 */
function manage_taxonomies_page($post_type) {
    $taxonomy_option_name = 'custom_taxonomies_' . $post_type;

    // Verarbeitung des Formulars zum Erstellen neuer Taxonomien
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST['taxonomy_name'])) {
        check_admin_referer('manage_taxonomies_nonce'); // Sicherheitsschutz

        $taxonomy_name = sanitize_text_field($_POST['taxonomy_name']);
        $existing_taxonomies = get_option($taxonomy_option_name, []);

        if (!in_array($taxonomy_name, $existing_taxonomies)) {
            $existing_taxonomies[] = $taxonomy_name;
            update_option($taxonomy_option_name, $existing_taxonomies);
            add_settings_error('manage_taxonomies', 'taxonomy_added', __('Taxonomie erfolgreich erstellt!', 'textdomain'), 'updated');
        }
    }

    // HTML-Formular zur Erstellung neuer Taxonomien
    echo '<div class="wrap">';
    echo '<h1>' . esc_html__('Taxonomie für ' . ucfirst($post_type) . ' verwalten', 'textdomain') . '</h1>';
    settings_errors('manage_taxonomies'); // Anzeige von Erfolgsmeldungen

    echo '<form method="POST">';
    wp_nonce_field('manage_taxonomies_nonce'); // Sicherheitstoken
    echo '<label for="taxonomy_name">' . __('Taxonomie-Name', 'textdomain') . ':</label>';
    echo '<input type="text" name="taxonomy_name" id="taxonomy_name" required>';
    echo '<br><br>';
    echo '<button type="submit" class="button button-primary">' . __('Taxonomie erstellen', 'textdomain') . '</button>';
    echo '</form>';
    echo '</div>';

    // Vorhandene Taxonomien anzeigen
    display_taxonomies($taxonomy_option_name);
}

// Zeigt vorhandene Taxonomien mit Lösch-Option
function display_taxonomies($taxonomy_option_name) {
    $existing_taxonomies = get_option($taxonomy_option_name, []);
    $post_type = str_replace('custom_taxonomies_', '', $taxonomy_option_name);

    if (!empty($existing_taxonomies)) {
        echo '<h2>' . __('Bestehende Taxonomien', 'textdomain') . '</h2>';
        echo '<ul>';
        foreach ($existing_taxonomies as $taxonomy) {
            echo '<li>' . esc_html($taxonomy) . ' 
            <a href="?post_type=' . esc_attr($post_type) . '&delete_taxonomy=' . esc_attr($taxonomy) . '" 
            onclick="return confirm(\'' . __('Möchten Sie diese Taxonomie wirklich löschen?', 'textdomain') . '\')">Löschen</a></li>';
        }
        echo '</ul>';
    } else {
        echo '<p>' . __('Keine Taxonomien gefunden.', 'textdomain') . '</p>';
    }
}

// Prüft, ob eine Taxonomie gelöscht werden soll
add_action('admin_init', function () {
    if (isset($_GET['delete_taxonomy']) && isset($_GET['post_type'])) {
        check_admin_referer('manage_taxonomies_nonce');

        $post_type = sanitize_text_field($_GET['post_type']);
        $taxonomy_option_name = 'custom_taxonomies_' . $post_type;
        $taxonomy_to_delete = sanitize_text_field($_GET['delete_taxonomy']);

        delete_taxonomy($taxonomy_option_name, $taxonomy_to_delete);
    }
});

// Entfernt eine Taxonomie und ihre Begriffe
function delete_taxonomy($taxonomy_option_name, $taxonomy_to_delete) {
    $terms = get_terms([
        'taxonomy' => $taxonomy_to_delete,
        'hide_empty' => false,
    ]);

    if (!empty($terms) && !is_wp_error($terms)) {
        foreach ($terms as $term) {
            wp_delete_term($term->term_id, $taxonomy_to_delete);
        }
    }

    $existing_taxonomies = get_option($taxonomy_option_name, []);
    if (($key = array_search($taxonomy_to_delete, $existing_taxonomies)) !== false) {
        unset($existing_taxonomies[$key]);
        update_option($taxonomy_option_name, $existing_taxonomies);
    }

    unregister_taxonomy($taxonomy_to_delete);
    add_settings_error('manage_taxonomies', 'taxonomy_deleted', __('Taxonomie gelöscht.', 'textdomain'), 'updated');
}

// Fügt Admin-Menüpunkte hinzu
add_action('admin_menu', function () {
    $post_types = ['social_wall', 'videothek', 'news'];

    foreach ($post_types as $post_type) {
        add_submenu_page(
            'edit.php?post_type=' . $post_type,
            __('Taxonomien verwalten', 'textdomain'),
            __('Taxonomien', 'textdomain'),
            'manage_options',
            'manage_' . $post_type . '_taxonomies',
            function () use ($post_type) {
                manage_taxonomies_page($post_type);
            }
        );
    }
});

// Registriert gespeicherte Taxonomien
add_action('init', function () {
    $post_types = ['social_wall', 'videothek', 'news'];

    foreach ($post_types as $post_type) {
        $taxonomy_option_name = 'custom_taxonomies_' . $post_type;
        $taxonomies = get_option($taxonomy_option_name, []);

        foreach ($taxonomies as $taxonomy) {
            register_taxonomy($taxonomy, $post_type, [
                'labels' => [
                    'name' => esc_html($taxonomy),
                    'singular_name' => esc_html($taxonomy),
                ],
                'public' => true,
                'hierarchical' => true,
                'show_ui' => true,
                'show_in_rest' => true,
            ]);
        }
    }
});

/**
 * Fügt SVG-Felder für Taxonomien hinzu
 */
function add_term_svg_field($taxonomy) {
    echo '<div class="form-field">
        <label for="term_svg">' . __('SVG Code', 'textdomain') . '</label>
        <textarea name="term_svg" id="term_svg" rows="5" class="large-text"></textarea>
        <p class="description">' . __('Fügen Sie hier den SVG-Code für diese Kategorie ein.', 'textdomain') . '</p>
    </div>';
}

function edit_term_svg_field($term, $taxonomy) {
    $svg_code = get_term_meta($term->term_id, 'term_svg', true);

    echo '<tr class="form-field">
        <th scope="row">
            <label for="term_svg">' . __('SVG Code', 'textdomain') . '</label>
        </th>
        <td>
            <textarea name="term_svg" id="term_svg" rows="5" class="large-text">' . esc_textarea($svg_code) . '</textarea>
            <p class="description">' . __('Fügen Sie hier den SVG-Code für diese Kategorie ein.', 'textdomain') . '</p>';
    if (!empty($svg_code)) {
        echo '<div class="svg-preview">
            <h4>' . __('SVG Vorschau:', 'textdomain') . '</h4>' . wp_kses_post($svg_code) . '
        </div>';
    }
    echo '</td></tr>';
}

// Speichert das SVG-Feld
function save_term_svg_field($term_id) {
    if (isset($_POST['term_svg'])) {
        $clean_svg = wp_kses($_POST['term_svg'], ['svg' => ['xmlns' => true, 'viewBox' => true], 'path' => ['d' => true]]);
        update_term_meta($term_id, 'term_svg', $clean_svg);
    }
}

// Registriert die SVG-Felder für alle benutzerdefinierten Taxonomien
add_action('admin_init', function () {
    $post_types = ['social_wall', 'videothek', 'news'];

    foreach ($post_types as $post_type) {
        $taxonomies = get_option('custom_taxonomies_' . $post_type, []);
        foreach ($taxonomies as $taxonomy) {
            add_action($taxonomy . '_add_form_fields', 'add_term_svg_field');
            add_action($taxonomy . '_edit_form_fields', 'edit_term_svg_field', 10, 2);
            add_action('created_' . $taxonomy, 'save_term_svg_field');
            add_action('edited_' . $taxonomy, 'save_term_svg_field');
        }
    }
});
