<?php


// Erstelle eine Funktion, die die einzigartigen Grid-Styles zurückgibt
function get_unique_videothek_grid_styles( $imageOnly = false, $slider_id ) {
    
    // Wenn imageOnly false ist, setze den Border-Radius nur oben (wie bei Deinem anderen Shortcode)
    if ( $imageOnly === 0 || $imageOnly === "0" ) {
        $css = '<style>
                .videothek-grid.' . esc_attr( $slider_id ) . ' .grid-item img {
                    border-radius: 12px 12px 0 0 !important;
                }
                </style>';
        return $css;
    }

    return '';
}

/**
 * Basis-Styles für das Video-Grid – statisch und unabhängig von DB-Werten.
 */
function get_videothek_grid_base_styles() {
    $text_color = get_option('vecura_mediathek_text_color', '#000000');
    $accent_color_primary = get_option('vecura_mediathek_accent_color_primary', '#000000');
    $videothek_slider_aspect_ratio_width = get_option('vecura_mediathek_videothek_slider_aspect_ratio_width', '3');
    $videothek_slider_aspect_ratio_height = get_option('vecura_mediathek_videothek_slider_aspect_ratio_height', '4');

    $css = '<style>
        main#content {
            max-width: 100vw;
            overflow: hidden;
        }

        .videthek-grid-outer-wrapper {
            padding: 0 0 3rem 0;
        }

        .videothek-grid-content p {
            margin: 1rem 0 2rem 0;
        }

        .videothek-grid-wrapper {
            color: ' . esc_attr($text_color) . ';
        }
        .videothek-grid .grid-item {
            background: ' . esc_attr($accent_color_primary) . ';
        }
        .videothek-grid .grid-item img {
            aspect-ratio: ' . esc_attr($videothek_slider_aspect_ratio_width) . ' / ' . esc_attr($videothek_slider_aspect_ratio_height) . ';
        }


        .videothek-grid-wrapper {
            margin: 0 auto;
            position: relative;
        }
        /* Responsive Grid Layout: Mobile 1 Spalte, Tablet 2, Desktop 4 */
        .videothek-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }
        @media screen and (min-width: 768px) {
            .videothek-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        @media screen and (min-width: 1024px) {
            .videothek-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }
        .videothek-grid .grid-item {
            border-radius: 16px;
            overflow: hidden;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            transition: all 240ms cubic-bezier(0,0,0.5,1);
        }
        .videothek-grid .grid-item:hover {
            scale: 1.025;
        }

        .videothek-grid .grid-item img {
            width: 100%;
            height: auto;
            border-radius: 12px;
            object-fit: cover;
        }
        .grid-content {
            padding: 0 1rem 1rem 1rem;
        }
        .grid-title {
            margin-top: 10px;
            font-size: 1.1em;
            font-weight: 500;
        }
        .grid-excerpt {
            margin-top: 0.5rem;
            overflow-wrap: break-word;
        }
    </style>';
    return $css;
}

/**
 * Shortcode für ein responsives Video-Grid
 * Desktop: 4 Spalten, Tablet: 2 Spalten, Mobile: 1 Spalte.
 */
function render_videothek_grid( $atts ) {
    // Standard-Attribute
    $default_atts = array(
        'headline'   => '',
        'tagline'    => '',
        'content'    => '',
        'count'      => -1,      // -1 = alle Videos anzeigen
        'image_only' => 'true',
        'debug'      => 'false'
    );
    $atts = is_array($atts) ? $atts : array();
    $processed_atts = shortcode_atts( $default_atts, $atts, 'videothek_grid' );
    
    // WP_Query für den Post-Type 'videothek'
    $args = array(
        'post_type'      => 'videothek',
        'posts_per_page' => intval( $processed_atts['count'] )
    );
    $query = new WP_Query( $args );
    
    // Zusammenführen der CSS-Styles: Basis + dynamische Werte
    $output = get_videothek_grid_base_styles();

    $output .= '<div class="videthek-grid-outer-wrapper">';
    
    // Ausgabe von Headline, Tagline und Content (falls gesetzt)
    if ( !empty( $processed_atts['tagline'] ) ) {
        $output .= '<h3 class="videothek-grid-tagline vecura-mediahub-shortcode-tagline">' . esc_html( $processed_atts['tagline'] ) . '</h3>';
    }
    if ( !empty( $processed_atts['headline'] ) ) {
        $output .= '<h2 class="videothek-grid-headline vecura-mediahub-shortcode-headline">' . esc_html( $processed_atts['headline'] ) . '</h2>';
    }
    if ( !empty( $processed_atts['content'] ) ) {
        $output .= '<div class="videothek-grid-content vecura-mediahub-shortcode-content"><p>' . wp_kses_post( $processed_atts['content'] ) . '</p></div>';
    }

    // In Deiner Shortcode-Funktion (render_videothek_grid) vor der Ausgabe:
    if ( function_exists( 'vecura_generate_random_id' ) ) {
        $slider_id = vecura_generate_random_id();
    } else {
        // Fallback, falls die Funktion nicht vorhanden ist
        $slider_id = 'funktion-nicht-verfuegbar';
    }

    // Umwandlung von String 'true'/'false' in echtes Boolean
    $imageOnly = filter_var( $processed_atts['image_only'], FILTER_VALIDATE_BOOLEAN ) ? 1 : 0;
    $styles = get_unique_videothek_grid_styles( $imageOnly, $slider_id );
    
    $output .= '<div class="videothek-grid-wrapper">';
    
    if ( $query->have_posts() ) {
        $output .= '<div class="videothek-grid ' . esc_attr($slider_id) . '">';
        while ( $query->have_posts() ) {
            $query->the_post();
            $output .= '<div class="grid-item">';
                $output .= '<a href="' . esc_url( get_permalink() ) . '">';
                    // Beitragsbild als Link zum Beitrag
                    if ( has_post_thumbnail() ) {
                        $output .= get_the_post_thumbnail( get_the_ID(), 'large' );
                    }
                    if ( ! $imageOnly) {
                        $output .= '<div class="grid-content">';
                        $output .= '<h4 class="grid-title">' . get_the_title() . '</h4>';
                        $output .= '<div class="grid-excerpt">' . get_the_excerpt() . '</div>';
                        $output .= '</div>';
                    }
                $output .= '</a>';
            $output .= '</div>'; // .grid-item
        }
        $output .= '</div>'; // .videothek-grid
        wp_reset_postdata();
    } else {
        $output .= '<p>' . __( 'Keine Videos gefunden.', 'textdomain' ) . '</p>';
    }
    
    $output .= '</div>'; // .videothek-grid-wrapper
    $output .= '</div>'; // .videothek-grid-outer-wrapper



    // Füge die generierten Styles in die Ausgabe ein
    $output .= $styles;
    
    return $output;
}
add_shortcode( 'videothek_grid', 'render_videothek_grid' );
