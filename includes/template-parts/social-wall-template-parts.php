<?
/* load nessesary scripts */ 
function enqueue_masonry_scripts() {
    wp_enqueue_script('masonry');
    wp_enqueue_script('imagesloaded');
    wp_enqueue_script('social-wall-masonry', plugins_url('../../js/social-wall-masonry.js', __FILE__), array('jquery', 'masonry', 'imagesloaded'), '1.0', true);
}
add_action('wp_enqueue_scripts', 'enqueue_masonry_scripts');


/* load css/styles */ 
function get_social_wall_styles() {
    $text_color = get_option('vecura_mediathek_text_color', '#000000');
    echo '<style>
        .social-wall-wrapper {
            /* max-width: 1200px; */
            margin: 0 auto;
            color: ' . esc_attr($text_color) . ';
            padding: 3rem 0;
        }

        .social-wall-content p {
            margin: 1rem 0 2rem 0;
        }

        .social-wall-wrapper .social-wall-posts {
            color: #112233;
            margin: 20px 0px;
        }
        .social-wall-wrapper .social-wall-post {
            width: calc(33.333% - (36px * 2 / 3)); /* Breite minus Gutter */
            /* width: 386.9px; */
            margin: 0 0px 20px 0px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 0px rgba(0,0,0,0.0);
            overflow: hidden;

            transition: box-shadow 300ms ease-in-out;
        }
        .social-wall-wrapper .social-wall-post:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .social-wall-wrapper .post-thumbnail {
            position: relative;
            padding-top: 56.25%; /* 16:9 Aspect Ratio */
            overflow: hidden;
        }
        .social-wall-wrapper .post-thumbnail img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 300ms ease;
        }
        .social-wall-wrapper .social-wall-post:hover .post-thumbnail img {
            transform: scale(1.05);
        }
        .social-wall-wrapper .post-content-wrapper {
            padding: 1rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .social-wall-wrapper .post-title {
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }
        .social-wall-wrapper .post-title a {
            color: #333;
            text-decoration: none;
        }
        .social-wall-wrapper .post-title a:hover {
            color: #0066cc;
        }

        .social-wall-wrapper .post-meta {
            padding: 0.5rem 1rem;
            font-size: 0.9em;
            display: flex;
            justify-content: space-between;
            /* align-items: center; */
        }

        /******************************/
        /* Different post meta styles */
        /******************************/
        /* Default */ 
        /*
        .social-wall-wrapper .post-meta.category-style-default .taxonomy-group:first-child .single-taxonomy-term:first-child {
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        */
        .social-wall-wrapper .post-meta.category-style-default .taxonomy-group .single-taxonomy-term {
            padding: 0;
            margin-bottom: 0.5rem;
            display: flex;
            gap: 0.5rem;
        }
        .social-wall-wrapper .single-taxonomy-term span.taxonomy-icon svg {
            width: 24px;
            height: 24px;
        }

        /* icons-only */
        .social-wall-wrapper .post-meta.category-style-icons-only .taxonomy-term {
            display: none;
        }
        .social-wall-wrapper .post-meta.category-style-icons-only .taxonomy-group {
            display: flex;
        }
        /*
        .social-wall-wrapper .post-meta.category-style-icons-only .taxonomy-group:first-child .single-taxonomy-term:first-child {
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        */
        .social-wall-wrapper .post-meta.category-style-icons-only .taxonomy-group .single-taxonomy-term {
            padding: 0;
            display: flex;
            gap: 0.5rem;
            margin-right: 0.5rem;
        }
        .social-wall-wrapper .post-meta.category-style-icons-only .single-taxonomy-term:not(:has(.taxonomy-icon)) {
            display: none !important;
        }


        /* End post meta styles */ 

        .social-wall-wrapper .post-content {
            flex-grow: 1;
            line-height: 1.6;
        }
        .social-wall-wrapper .read-more {
            display: inline-block;
            margin-top: 15px;
            padding: 8px 16px;
            background-color: #0066cc;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 300ms ease;
        }
        .social-wall-wrapper .read-more:hover {
            background-color: #0052a3;
        }
        .social-wall-wrapper .no-thumbnail {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        @media (max-width: 992px) {
            .social-wall-wrapper .social-wall-posts {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        @media (max-width: 576px) {
            .social-wall-wrapper .social-wall-posts {
                grid-template-columns: 1fr;
            }
        }

        /* Für mittlere Bildschirme: 2 Spalten */
        @media (max-width: 992px) {
            .social-wall-wrapper .social-wall-post {
                width: calc(50% - 18px); /* Gutter eventuell anpassen */
            }
        }

        /* Für kleine Bildschirme: 1 Spalte */
        @media (max-width: 576px) {
            .social-wall-wrapper .social-wall-post {
                width: 100%;
            }
        }

        /* Styling für den Mehr laden Button */
        .load-more-social {
            display: block;
            margin: 2rem auto;
            padding: 12px 24px;
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 1em;
            cursor: pointer;
            transition: background-color 300ms ease, transform 150ms ease;
        }

        .load-more-social:hover {
            background-color: #0052a3;
            transform: translateY(-1px);
        }

        .load-more-social:active {
            transform: translateY(0);
        }

        .load-more-social.loading {
            opacity: 0.7;
            cursor: wait;
        }
    </style>';
}
add_action('wp_head', 'get_social_wall_styles');

/* render html */ 
// Komplexer Shortcode für Social Wall mit Unterstützung für mehrere benutzerdefinierte Taxonomien
function render_social_wall_post($atts) {
    // Settings laden
    $text_color = get_option('vecura_mediathek_text_color', '#000000');
    $category_style = get_option('vecura_mediathek_social_wall_category_style', 'default');
    $hide_categories = get_option('vecura_mediathek_social_wall_hide_categories', false);
    $hide_date = get_option('vecura_mediathek_social_wall_hide_date', false);
    $cta_text = get_option('vecura_mediathek_social_wall_cta_text', false);
    $category_class = 'category-style-' . $category_style;

    // Standardwerte für die Attribute
    $atts = shortcode_atts(array(
        'headline' => '',
        'tagline' => '',
        'content' => '',
        'id' => '',
        'count' => 9,
    ), $atts, 'social_wall');

    // Eindeutige ID für diesen Aufruf
    $wrapper_id = !empty($atts['id']) ? esc_attr($atts['id']) : 'social-wall-' . uniqid();
    
    // Eine eindeutige Container-ID, die sich von der Wrapper-ID unterscheidet
    $container_id = $wrapper_id . '-container';

    // Query-Argumente
    $args = array(
        'post_type' => 'social_wall',
        'posts_per_page' => intval($atts['count'])
    );

    // Taxonomie-Filter
    $tax_query = array();
    foreach ($atts as $key => $value) {
        if (!in_array($key, array('headline', 'tagline', 'content', 'id', 'count')) && !empty($value) && $value !== 'all') {
            $tax_query[] = array(
                'taxonomy' => $key,
                'field' => 'slug',
                'terms' => explode(',', $value)
            );
        }
    }

    if (!empty($tax_query)) {
        $args['tax_query'] = array_merge(array('relation' => 'AND'), $tax_query);
    }

    // Hole aktuelle Taxonomie-Filter
    $current_taxonomies = array();
    $taxonomies = get_object_taxonomies('social_wall', 'objects');
    foreach ($taxonomies as $tax_slug => $taxonomy) {
        $terms = get_terms(array(
            'taxonomy' => $tax_slug,
            'hide_empty' => true
        ));
        if (!empty($terms) && !is_wp_error($terms)) {
            $current_taxonomies[$tax_slug] = wp_list_pluck($terms, 'slug');
        }
    }

    $output = '<div class="social-wall-wrapper" id="' . $wrapper_id . '">';

    if (!empty($atts['tagline'])) {
        $output .= '<h3 class="social-wall-tagline vecura-mediahub-shortcode-tagline">' . esc_html($atts['tagline']) . '</h3>';
    }

    if (!empty($atts['headline'])) {
        $output .= '<h2 class="social-wall-headline vecura-mediahub-shortcode-headline">' . esc_html($atts['headline']) . '</h2>';
    }

    if (!empty($atts['content'])) {
        $output .= '<div class="social-wall-content vecura-mediahub-shortcode-content"><p>' . wp_kses_post($atts['content']) . '</p></div>';
    }

    // Query ausführen
    $query = new WP_Query($args);
    $max_pages = $query->max_num_pages;

    if ($query->have_posts()) {
        // Container für Posts mit eindeutiger ID, die sich von der Wrapper-ID unterscheidet
        $output .= '<div id="' . $container_id . '" class="social-wall-posts" data-taxonomies="' . esc_attr(json_encode($current_taxonomies)) . '">';
        
        while ($query->have_posts()) {
            $query->the_post();
            
            $output .= '<article class="social-wall-post">';

            /*
             * Taxonomien 
             */
            if (!$hide_categories) {
                $output .= '<div class="post-meta ' . esc_attr($category_class) . '">';
                
                $output .= '<div class="post-taxonomies">';

                    // Holen Sie die benutzerdefinierten Taxonomien für social_wall
                    $custom_taxonomies = get_option('custom_taxonomies_social_wall', array());
            
                    if (!empty($custom_taxonomies)) {
                        foreach ($custom_taxonomies as $taxonomy) {
                            $terms = get_the_terms(get_the_ID(), $taxonomy);
                            
                            if ($terms && !is_wp_error($terms)) {
                                $output .= '<div class="taxonomy-group">';

                                // Taxonomy name for debugging
                                // $output .= '<span class="taxonomy-name">' . esc_html($taxonomy) . ':</span>';

                                foreach ($terms as $term) {
                                    $svg_code = get_term_meta($term->term_id, 'term_svg', true);

                                    $output .= '<div class="single-taxonomy-term">';

                                        // SVG als zusätzliche Information ausgeben, falls vorhanden
                                        $svg_code = get_term_meta($term->term_id, 'term_svg', true);
                                        if (!empty($svg_code)) {
                                            $output .= '<span class="taxonomy-icon" title="' . esc_attr($term->name) . '">';
                                            $output .= $svg_code;
                                            $output .= '</span>';
                                        }

                                        // output category term name
                                        $output .= '<span class="taxonomy-term">' . esc_html($term->name) . '</span>';

                                    $output .= '</div>';
                                }
                                $output .= '</div>';
                            }
                        }
                    }
                    $output .= '</div>'; // Ende post-taxonomies

                    // Datum hinzufügen
                    $output .= '<div class="post-date">' . get_the_date() . '</div>';

                $output .= '</div>'; // Ende post-meta
            }
            
            // Beitragsbild
            $output .= '<div class="post-thumbnail">';
            if (has_post_thumbnail()) {
                $output .= '<a href="' . get_permalink() . '">';
                $output .= get_the_post_thumbnail(get_the_ID(), 'large');
                $output .= '</a>';
            } else {
                $output .= '<div class="no-thumbnail">';
                $output .= __('Kein Beitragsbild vorhanden', 'textdomain');
                $output .= '</div>';
            }
            $output .= '</div>';
            
            $output .= '<div class="post-content-wrapper">';
            $output .= '<h4 class="post-title">';
            // $output .= '<a href="' . get_permalink() . '">';
            $output .= get_the_title();
            // $output .= '</a>';
            $output .= '</h4>';
            
            $output .= '<div class="post-content">';
            $output .= get_the_excerpt();

            $output .= '<div class="post-cta">';
            if($cta_text) {
                $output .= '<a href="' . get_permalink() . '" class="read-more">';
                $output .= $cta_text;
                $output .= '</a>';
            } else {
                $output .= '<a href="' . get_permalink() . '" class="read-more">' . 
                          __('Weiterlesen', 'textdomain') . 
                          '</a>';
            } 
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</div>'; // Ende post-content-wrapper
            
            $output .= '</article>';
        }
        $output .= '</div>'; // Ende social-wall-posts

        // "Mehr laden"-Button hinzufügen, wenn es weitere Seiten gibt
        if ($max_pages > 1) {
            $output .= '<button class="load-more-social" data-container-id="' . esc_attr($container_id) . '" data-page="1" data-max="' . $max_pages . '" data-count="' . intval($atts['count']) . '">'
                        . __('Mehr laden', 'textdomain') . 
                        '</button>';
        }

        wp_reset_postdata();
    }

    $output .= '</div>';

    return $output;
}
add_shortcode('social_wall', 'render_social_wall_post');


/* AJAX Handler für "Mehr laden" */
function load_more_social() {
    // Nonce-Check
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'vecura_mediathek_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
    $count = isset($_POST['count']) ? intval($_POST['count']) : 9;

    // Hole die Einstellungen
    $category_style = get_option('vecura_mediathek_social_wall_category_style', 'default');
    $hide_categories = get_option('vecura_mediathek_social_wall_hide_categories', false);
    $hide_date = get_option('vecura_mediathek_social_wall_hide_date', false);
    $cta_text = get_option('vecura_mediathek_social_wall_cta_text', false);
    $category_class = 'category-style-' . $category_style;

    // Query-Argumente
    $args = array(
        'post_type'      => 'social_wall',
        'posts_per_page' => $count,
        'paged'          => $page,
        'post_status'    => 'publish'
    );

    // Taxonomie-Filter aus der ursprünglichen Query übernehmen
    if (isset($_POST['taxonomies']) && !empty($_POST['taxonomies'])) {
        $tax_query = array();
        foreach ($_POST['taxonomies'] as $taxonomy => $terms) {
            if (!empty($terms)) {
                $tax_query[] = array(
                    'taxonomy' => sanitize_text_field($taxonomy),
                    'field'    => 'slug',
                    'terms'    => is_array($terms) ? array_map('sanitize_text_field', $terms) : array(sanitize_text_field($terms))
                );
            }
        }
        if (!empty($tax_query)) {
            $args['tax_query'] = array_merge(array('relation' => 'AND'), $tax_query);
        }
    }

    $query = new WP_Query($args);
    $output = '';

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            
            $output .= '<article class="social-wall-post">';
            
            if (!$hide_categories) {
                $output .= '<div class="post-meta ' . esc_attr($category_class) . '">';
                $output .= '<div class="post-taxonomies">';

                $custom_taxonomies = get_option('custom_taxonomies_social_wall', array());
        
                if (!empty($custom_taxonomies)) {
                    foreach ($custom_taxonomies as $taxonomy) {
                        $terms = get_the_terms(get_the_ID(), $taxonomy);
                        
                        if ($terms && !is_wp_error($terms)) {
                            $output .= '<div class="taxonomy-group">';
                            foreach ($terms as $term) {
                                $output .= '<div class="single-taxonomy-term">';
                                $svg_code = get_term_meta($term->term_id, 'term_svg', true);
                                if (!empty($svg_code)) {
                                    $output .= '<span class="taxonomy-icon" title="' . esc_attr($term->name) . '">';
                                    $output .= $svg_code;
                                    $output .= '</span>';
                                }
                                $output .= '<span class="taxonomy-term">' . esc_html($term->name) . '</span>';
                                $output .= '</div>';
                            }
                            $output .= '</div>';
                        }
                    }
                }
                $output .= '</div>';
                if (!$hide_date) {
                    $output .= '<div class="post-date">' . get_the_date() . '</div>';
                }
                $output .= '</div>';
            }
            
            $output .= '<div class="post-thumbnail">';
            if (has_post_thumbnail()) {
                $output .= '<a href="' . get_permalink() . '">';
                $output .= get_the_post_thumbnail(get_the_ID(), 'large');
                $output .= '</a>';
            } else {
                $output .= '<div class="no-thumbnail">';
                $output .= __('Kein Beitragsbild vorhanden', 'textdomain');
                $output .= '</div>';
            }
            $output .= '</div>';
            
            $output .= '<div class="post-content-wrapper">';
            $output .= '<h4 class="post-title">';
            $output .= get_the_title();
            $output .= '</h4>';
            
            $output .= '<div class="post-content">';
            $output .= get_the_excerpt();

            $output .= '<div class="post-cta">';
            if($cta_text) {
                $output .= '<a href="' . get_permalink() . '" class="read-more">';
                $output .= $cta_text;
                $output .= '</a>';
            } else {
                $output .= '<a href="' . get_permalink() . '" class="read-more">' . 
                      __('Weiterlesen', 'textdomain') . 
                      '</a>';
            } 
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</div>';
            
            $output .= '</article>';
        }
        wp_reset_postdata();
    }
    
    echo $output;
    wp_die();
}
add_action('wp_ajax_load_more_social', 'load_more_social');
add_action('wp_ajax_nopriv_load_more_social', 'load_more_social');


/* Enqueue das neue JavaScript */
function enqueue_social_wall_loadmore_script() {
    $script_path = plugin_dir_path(__FILE__) . '../../js/social-wall-loadmore.js';
    $script_url = plugins_url('../../js/social-wall-loadmore.js', __FILE__);
    
    if (file_exists($script_path)) {
        $version = filemtime($script_path);
    } else {
        $version = '1.0';
    }

    wp_enqueue_script(
        'social-wall-loadmore',
        $script_url,
        array('jquery', 'masonry', 'imagesloaded'),
        $version,
        true
    );

    wp_localize_script('social-wall-loadmore', 'social_wall_ajax_object', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce'    => wp_create_nonce('vecura_mediathek_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'enqueue_social_wall_loadmore_script');