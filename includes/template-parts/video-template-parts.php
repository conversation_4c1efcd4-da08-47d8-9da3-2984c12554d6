<?php
/**
 * Shortcode zur Darstellung der Videothek
 */



/* Lade News-spezifische CSS-Styles */
function get_unique_video_slider_styles($imageOnly = false, $slider_id) {
    $text_color = get_option('vecura_mediathek_text_color', '#000000');
    $accent_color_primary = get_option('vecura_mediathek_accent_color_primary', '#000000');
    $accent_color_secondary = get_option('vecura_mediathek_accent_color_secondary', '#000000');
    $accent_color_tertiary = get_option('vecura_mediathek_accent_color_tertiary', '#000000');
    $background_color = get_option('vecura_mediathek_background_color', '#000000');

    // videothek options 
    $videothek_slider_aspect_ratio_width = get_option('vecura_mediathek_videothek_slider_aspect_ratio_width', '3');
    $videothek_slider_aspect_ratio_height = get_option('vecura_mediathek_videothek_slider_aspect_ratio_height', '4');
    $videothek_hero_aspect_ratio_width   = get_option('vecura_mediathek_videothek_hero_aspect_ratio_width', '16');
    $videothek_hero_aspect_ratio_height  = get_option('vecura_mediathek_videothek_hero_aspect_ratio_height', '9');


    $css = '<style>
        .videothek-wrapper.' . esc_attr($slider_id) . '  {
            padding: 3rem 0;
        }';

        // Wenn imageOnly false ist, füge zusätzliche CSS-Regeln hinzu:
        if ($imageOnly === 0 || $imageOnly === "0") {
            $css .= '.videothek-wrapper.' . esc_attr($slider_id) . ' article.vecura-slide__item img {
                border-radius: 12px 12px 0 0 !important;
                /* Hier könntest Du weitere Styles setzen */
            }';
        }

        $css .= '</style>';

        return $css;
}
// add_action('wp_head', 'get_unique_video_slider_styles');


/* Lade Video-spezifische CSS-Styles, falls noch nicht geladen */
function get_video_slider_styles() {
    static $styles_loaded = false;
    if ($styles_loaded) {
        return;
    }
    $styles_loaded = true;

    $text_color = get_option('vecura_mediathek_text_color', '#000000');
    $accent_color_primary = get_option('vecura_mediathek_accent_color_primary', '#000000');
    $accent_color_secondary = get_option('vecura_mediathek_accent_color_secondary', '#000000');
    $accent_color_tertiary = get_option('vecura_mediathek_accent_color_tertiary', '#000000');
    $background_color = get_option('vecura_mediathek_background_color', '#000000');

    // videothek options 
    $videothek_slider_aspect_ratio_width = get_option('vecura_mediathek_videothek_slider_aspect_ratio_width', '3');
    $videothek_slider_aspect_ratio_height = get_option('vecura_mediathek_videothek_slider_aspect_ratio_height', '4');
    $videothek_hero_aspect_ratio_width   = get_option('vecura_mediathek_videothek_hero_aspect_ratio_width', '16');
    $videothek_hero_aspect_ratio_height  = get_option('vecura_mediathek_videothek_hero_aspect_ratio_height', '9');
    
    echo '<style>
        main#content {
            max-width: 100vw;
            overflow: hidden;
        }

        .videothek-content p {
            margin: 1rem 0 2rem 0;
        }

        .videothek-wrapper {
            margin: 0 auto;
            color: ' . esc_attr($text_color) . ';
            padding: 3rem 0;
            position: relative;
        }

        .videothek-wrapper * {
            -webkit-user-select: none; /* Safari */
            -ms-user-select: none; /* IE 10+ und Edge */
            user-select: none; /* Standard-Syntax */
        }

        .videothek-wrapper .vecura-slide__wrapper-inner {
            display: flex;
            gap: 20px;
            align-items: center;
            position: relative;
        }

        .post-thumbnail {
            position: relative;
        }

        .vecura-slide.hero-slider article.vecura-slide__item img {
            pointer-events: none;
            border-radius: 12px;
            aspect-ratio: ' . esc_attr($videothek_hero_aspect_ratio_width) . ' / ' . esc_attr($videothek_hero_aspect_ratio_height) . ';
            width: 100%;
            object-fit: cover;
        }

        /* Taxonomies innerhalb von bzw. auf Post Image */ 
        .post-taxonomies ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        .post-taxonomies ul li {
            color: ' . esc_attr($text_color) . ';
            background: ' . esc_attr($accent_color_primary) . ';
            padding: 0.1rem 0.5rem;
            border-radius: 0.5rem;
        }


        .videothek-wrapper .arrow-wrapper {
            z-index: 9;
            position: absolute;
            width: 100%;
            display: block;
            display: flex;
            justify-content: space-between;
            pointer-events: none;
        }
        .videothek-wrapper .arrow {
            width: 96px;
            height: 96px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            z-index: 9;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: all;
        }
        .videothek-wrapper .arrow.left {
            transform: translate(-48px, 0);
        }
        .videothek-wrapper .arrow.right {
            transform: translate(48px, 0);
        }

        .videothek-wrapper .arrow.inactive {
            opacity: 0 !important;
            pointer-events: none;
            z-index: -9 !important;
        }    

        
        .vecura-slide__list {
            display: flex;
            gap: 20px;
            position: relative;
            margin-left: 0;
            transition: all 0.66s cubic-bezier(0.77, 0.2, 0.05, 1);
            transition: all 0.33s cubic-bezier(0,0,0.5,1);
        }
        article.vecura-slide__item {
            max-width: 100%;
            /* width: calc(25% - 20px); */
            border-radius: 16px;
            list-style: none;
            position: relative;
            cursor: grab;
            user-select: none;
            scale: 1;
            box-sizing: border-box; 

            /* min-width: 100%; */ 
            /** Vorsicht – muss in JS zurückgesetzt werden. Ggf. hoher Layout Shift */ 

            transition: all 240ms cubic-bezier(0,0,0.5,1);
            transition-property: scale, transform, opacity;
        }

        article.vecura-slide__item:hover {
            scale: 1.025;
        }
        .vecura-slide.hero-slider article.vecura-slide__item:hover {
            scale: 1;
        }
        .is-dragging article.vecura-slide__item:hover {
            scale: 1;
        }

        .post-title, .post-content {
            width: 100%;
            overflow-wrap: break-word; /* Sorgt dafür, dass lange Wörter umbrechen */
            word-wrap: break-word;
            hyphens: auto; /* Erlaubt Silbentrennung */
        }

        .post-content {
            display: -webkit-box;
            -webkit-line-clamp: 3; /* Beschränkt den Text auf 3 Zeilen */
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis; /* Zeigt Auslassungspunkte an, wenn Text abgeschnitten wird */
        }

        /* TODO: if condition */ 
        /* if used with background or wich accent color to use */ 
        article.vecura-slide__item {
            background: ' . esc_attr($accent_color_primary) . ';
            position: ralative;
            display: flex;
            flex-direction: column;
        }

        article.vecura-slide__item img {
            pointer-events: none;
            border-radius: 12px;
            aspect-ratio: ' . esc_attr($videothek_slider_aspect_ratio_width) . ' / ' . esc_attr($videothek_slider_aspect_ratio_height) . ';
            object-fit: cover;
        }

        .post-text-wrapper {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: baseline;
            flex-grow: 1;
        }

        .post-taxonomies-wrapper {
            padding: 0.75rem 0.5rem;
            position: absolute;
            bottom: 0;
            width: 100%;
        }


        .post-content-wrapper {
            box-sizing: border-box; 
            width: 100%;
        }

        /* Alternative Vorgehensweils falls padding Probleme macht */ 
        .inner-content {
            box-sizing: border-box;
            width: calc(100% - 2rem);
            margin: auto;
        }

        /*
        .inner-content {
            box-sizing: border-box;
            padding: 1rem;
        }
        */

        h4.post-title {
            font-weight: 500;
            margin: 0.5rem 0 0.5rem 0;
            box-sizing: border-box;
            overflow: hidden;
            box-sizing: border-box !important;
            text-wrap: balance;
        }
        .post-content {
            margin: 0 0 1rem 0;
            padding: 0;
            box-sizing: border-box; 
            text-wrap: pretty;
        }   




        /* Slider indicator */
        .slider-indicator {
            margin-top: 3rem;
            display: flex;
            justify-content: center;
            position: relative;
            z-index: 9;
        }
        .slider-indicator-item {
            width: 30px;
            height: 6px;
            display: block;
            background: #afafc7;
            margin: 0px 1%;
            cursor: pointer;

            /* new style */
            height: 10px;
            border-radius: 4px;

            transition: all 0.66s cubic-bezier(0.77, 0.2, 0.05, 1);
        }

        .slider-indicator-item.active {
            width: 70px;
            background: ' . esc_attr($accent_color_primary) . ';
        }

        .slider-indicator-item * {
            pointer-events: none;
        }

        .slider-indicator-item:first-child {
            margin-left: 0;
        }
        .slider-indicator-item:last-child {
            margin-right: 0;
        }



        /* Slider position Display */ 
        .slider-position-display {
            margin-top: 2rem;
            position: relative;
            display: none;
            width: 100%;
            height: 4px;
            background: rgba(0, 0, 0, 0.1);
            overflow: hidden;
            opacity: 0; /* Startet unsichtbar */
            background: rgba(0,0,0,0.5);
            height: 10px;
            border-radius: 4px;
            transition: all 0.66s cubic-bezier(0.77, 0.2, 0.05, 1);
        }
        .slider-position-display span {
            display: block;
            border-radius: 4px;
            height: 100%;
            background: ' . esc_attr($accent_color_primary) . ';
            transition: transform 0.3s;
        }
        
        /* Hero Style */ 
        .videothek-wrapper.hero-slider:before {
            content: "";
            position: absolute;
            width: 100vw;
            height: 100%;
            /* background: hsla(240, 7%, 97%, 1); */
            top: 0;
            left: calc(50% - 50vw);
            z-index: 0;
        }
        .vecura-slide hero-slider {
            z-index: 9;
        }
        
        .videothek-cta-wrapper {
            display: flex;
            justify-content: center;
            padding: 2rem 0 0 0;
        }

        .videothek-cta-wrapper a {
            padding: 0.75rem 1.25rem;
            background: ' . esc_attr($accent_color_primary) . ';;
            border-radius: 999px;
            color: ' . esc_attr($text_color) . ';

            transition: all 240ms cubic-bezier(0,0,0.5,1);
        }
        .videothek-cta-wrapper a:hover {
            padding: 0.75rem 1.25rem;
            background: ' . esc_attr($text_color) . ';;
            border-radius: 999px;
            color: ' . esc_attr($accent_color_primary) . ';
        }
        
        </style>';
}
add_action('wp_head', 'get_video_slider_styles');


function render_videothek_slider($atts) {
    // Standardwerte für den Shortcode
    $default_atts = array(
        'id'       => '',

        'headline' => '',
        'tagline'  => '',
        'content'  => '',

        'image_only' => 'true',
        'count'    => 6, // Standardmäßig 6 Videos anzeigen
        'aggregation' => 'false', // 'true' -> slided alles auf einmal, 'false' -> einzeln sliden
        'max_slides' => 4, // Anzahl der sichtbaren Slides
        'gap' => '20px', // Abstand zwischen Slides
        'slider_indicator' => 'indicators', // Standard: Slider Indicators
        'hero_slider'    => 'false',
        'use_arrows' => 'true',
        'debug'     => 'false', // Debug-Modus für die Fehlersuche
        'showtaxonomies' => '', // Komma-getrennte Liste von anzuzeigenden Taxonomien

        'cta_text' => '',
        'cta_url' => '',
    );
    
    // Konvertiere $atts in ein Array, falls es keins ist
    $atts = is_array($atts) ? $atts : array();
    
    // Hole alle registrierten Taxonomien für den Post-Type 'videothek'
    $videothek_taxonomies = get_object_taxonomies('videothek', 'objects');
    
    // Erstelle ein $atts Array um die Shortcode-Attribute zu verarbeiten
    $processed_atts = shortcode_atts($default_atts, $atts, 'videothek');
    
    if (function_exists('vecura_generate_random_id')) {
        $slider_id = vecura_generate_random_id();
    } else {
        // Debug-Ausgabe oder alternative Logik
        $slider_id = 'Funktion nicht verfügbar';
    }

    // Hole die CSS-Styles mit Übergabe des Parameters
    // Umwandlung von String 'true'/'false' in echtes Boolean
    $imageOnly = filter_var($processed_atts['image_only'], FILTER_VALIDATE_BOOLEAN) ? 1 : 0;
    $styles = get_unique_video_slider_styles($imageOnly, $slider_id);
    $output = $styles;

    // Query für die Videothek-Beiträge
    $args = array(
        'post_type'      => 'videothek',
        'posts_per_page' => intval($processed_atts['count']),
    );
    
    // Debug-Ausgabe, wenn aktiviert
    $debug_output = '';
    $debug_mode = filter_var($processed_atts['debug'], FILTER_VALIDATE_BOOLEAN);
    
    if ($debug_mode) {
        $debug_output .= '<div style="background: #f5f5f5; border: 1px solid #ddd; padding: 15px; margin: 15px 0; font-family: monospace;">';
        $debug_output .= '<h3>Debug-Informationen:</h3>';
        
        // Originaler Input-Array (unbearbeitet)
        $debug_output .= '<h4>Originaler $atts Array:</h4>';
        $debug_output .= '<pre>' . print_r($atts, true) . '</pre>';
        
        // Konvertiere alle Schlüssel in Kleinbuchstaben für einen besseren Vergleich
        $lowercase_atts = array();
        foreach ($atts as $key => $value) {
            $lowercase_atts[strtolower($key)] = $value;
        }
        
        $debug_output .= '<h4>$atts mit Kleinbuchstaben-Schlüsseln:</h4>';
        $debug_output .= '<pre>' . print_r($lowercase_atts, true) . '</pre>';
        
        $debug_output .= '<h4>Verarbeitete Standard-Attribute:</h4>';
        $debug_output .= '<pre>' . print_r($processed_atts, true) . '</pre>';
        
        // Detaillierte Taxonomie-Informationen
        $debug_output .= '<h4>Verfügbare Taxonomien für "videothek":</h4>';
        $tax_debug = array();
        foreach ($videothek_taxonomies as $tax) {
            $tax_debug[$tax->name] = array(
                'singular_name' => $tax->labels->singular_name,
                'plural_name' => $tax->labels->name,
                'lowercase_name' => strtolower($tax->name),
                'lowercase_singular' => strtolower($tax->labels->singular_name),
                'lowercase_plural' => strtolower($tax->labels->name)
            );
        }
        $debug_output .= '<pre>' . print_r($tax_debug, true) . '</pre>';
    }
    
    // Verarbeite die dynamischen Taxonomie-Filter
    $tax_query = array();
    $found_taxonomies = array();
    
    // Erstelle ein Array mit Kleinbuchstaben für besseren Vergleich
    $lowercase_atts = array();
    foreach ($atts as $key => $value) {
        $lowercase_atts[strtolower($key)] = $value;
    }
    
    foreach ($videothek_taxonomies as $taxonomy) {
        $tax_name = $taxonomy->name;
        $tax_label = $taxonomy->labels->singular_name;
        $tax_plural = $taxonomy->labels->name;
        
        // Verschiedene Varianten für den Vergleich
        $variants = array(
            $tax_name,                      // Originaler Name
            $tax_label,                     // Singular-Label
            $tax_plural,                    // Plural-Label
            strtolower($tax_name),          // Kleinbuchstaben-Name
            strtolower($tax_label),         // Kleinbuchstaben-Singular
            strtolower($tax_plural)         // Kleinbuchstaben-Plural
        );
        
        $found_key = null;
        $found_value = null;
        
        // Prüfe alle Varianten
        foreach ($variants as $variant) {
            if (isset($atts[$variant])) {
                $found_key = $variant;
                $found_value = $atts[$variant];
                break;
            }
            // Prüfe auch mit Kleinbuchstaben
            if (isset($lowercase_atts[$variant])) {
                $found_key = $variant;
                $found_value = $lowercase_atts[$variant];
                break;
            }
        }
        
        if ($found_key && $found_value) {
            $found_taxonomies[$tax_name] = array(
                'found_via' => $found_key,
                'original_key' => $tax_name,
                'label' => $tax_label,
                'value' => $found_value
            );
            
            $tax_query[] = array(
                'taxonomy' => $tax_name,
                'field'    => 'slug',
                'terms'    => sanitize_text_field($found_value),
            );
        }
    }
    
    if ($debug_mode) {
        $debug_output .= '<h4>Gefundene Taxonomie-Filter:</h4>';
        $debug_output .= '<pre>' . print_r($found_taxonomies, true) . '</pre>';
        
        $debug_output .= '<h4>Resultierende Tax Query:</h4>';
        $debug_output .= '<pre>' . print_r($tax_query, true) . '</pre>';
    }
    
    // Füge Tax Query zur Hauptabfrage hinzu, wenn Filter gesetzt wurden
    if (!empty($tax_query)) {
        if (count($tax_query) > 1) {
            $tax_query['relation'] = 'AND'; // Alle Taxonomie-Bedingungen müssen erfüllt sein
        }
        $args['tax_query'] = $tax_query;
    }
    
    if ($debug_mode) {
        $debug_output .= '<h4>WP_Query Arguments:</h4>';
        $debug_output .= '<pre>' . print_r($args, true) . '</pre>';
        $debug_output .= '</div>';
    }

    $query = new WP_Query($args);
    
    // Füge Debug-Ausgabe hinzu, wenn aktiviert
    if ($debug_mode) {
        $output .= $debug_output;
    }

    $output .= '<div class="videothek-wrapper ' . esc_attr($slider_id) . ' ' . ( filter_var($processed_atts['hero_slider'], FILTER_VALIDATE_BOOLEAN) ? ' hero-slider' : '' ) . '" 
                ' . (!empty($processed_atts['id']) ? ' id="' . esc_attr($processed_atts['id']) . '"' : '') . 
                '>';

    if (!empty($processed_atts['tagline'])) {
        $output .= '<h3 class="videothek-tagline vecura-mediahub-shortcode-tagline">' . esc_html($processed_atts['tagline']) . '</h3>';
    }

    if (!empty($processed_atts['headline'])) {
        $output .= '<h2 class="videothek-headline vecura-mediahub-shortcode-headline">' . esc_html($processed_atts['headline']) . '</h2>';
    }

    if (!empty($processed_atts['content'])) {
        $output .= '<div class="videothek-content vecura-mediahub-shortcode-content"><p>' . wp_kses_post($processed_atts['content']) . '</p></div>';
    }

    // Umwandlung von String 'true'/'false' in echtes Boolean
    $aggregation = filter_var($processed_atts['aggregation'], FILTER_VALIDATE_BOOLEAN);
    // Aggregations-Logik: Falls `true`, dann `max_slides`, sonst `1`
    $sliderAggregator = $aggregation ? intval($processed_atts['max_slides']) : 1;

    if ($query->have_posts()) {
        // ✅ Slider-Wrapper
        $output .= '<div class="vecura-slide' . ( filter_var($processed_atts['hero_slider'], FILTER_VALIDATE_BOOLEAN) ? ' hero-slider' : '' ) . '" 
                        data-side-aggregation=' . esc_attr($sliderAggregator) .' 
                        data-slide-max-slides="' . esc_attr($processed_atts['max_slides']) . '" 
                        data-slide-gap="' . esc_attr($processed_atts['gap']) . '" 
                        data-slide-indicator="' . esc_attr($processed_atts['slider_indicator']) . '"
                        data-hero-slider="' . esc_attr($processed_atts['hero_slider']) . '"
                        data-use-arrows="' . esc_attr($processed_atts['use_arrows']) . '"
                        style="opacity: 0;"
                    >';
        $output .= '<div class="vecura-slide__wrapper">';
        $output .= '<div class="vecura-slide__wrapper-inner">';

        $output .= '<div class="arrow-wrapper">';
        $output .= '<div class="arrow left inactive">
                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m15 19-7-7 7-7"/>
                        </svg>
                    </div>';
        $output .= '<div class="arrow right inactive">
                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
                        </svg>
                    </div>';
        $output .= '</div>';

        $output .= '<div class="vecura-slide__list">';

        while ($query->have_posts()) {
            $query->the_post();

            // ✅ Slide-Item
            $output .= '<article class="vecura-slide__item" data-link-url="' . get_permalink() . '">';
            
            // Beitragsbild
            if (has_post_thumbnail()) {
                $output .= '<div class="post-thumbnail">';


                    /**
                     * Taxonomie Wrapper 
                     * // TODO: ggf. admin settings entscheiden lass, ob diese hier oder unter der Bild ausgespielt werden
                     */
                    $output .= '<div class="post-taxonomies-wrapper">';
                        $taxonomies = get_object_taxonomies('videothek');
                        
                        // Überprüfe, ob bestimmte Taxonomien angezeigt werden sollen
                        $show_taxonomies = array();
                        $taxonomies_debug = ''; // Separate Debug-Variable für diese Funktion
                        
                        if (!empty($processed_atts['showtaxonomies'])) {
                            // Trenne bei Komma und entferne Leerzeichen
                            $requested_taxonomies = array_map('trim', explode(',', $processed_atts['showtaxonomies']));
                            
                            // Erstelle ein Array mit Kleinbuchstaben für besseren Vergleich
                            $lowercase_requested = array_map('strtolower', $requested_taxonomies);
                            
                            $taxonomies_debug .= '<h4 style="padding: 0; color: #000;">Angeforderte Taxonomien:</h4>';
                            $taxonomies_debug .= '<pre style="padding: 0; color: #000;">' . print_r($requested_taxonomies, true) . '</pre>';
                            
                            // Prüfe jede verfügbare Taxonomie
                            foreach ($taxonomies as $taxonomy_name) {
                                $taxonomy_object = get_taxonomy($taxonomy_name);
                                $tax_variants = array(
                                    $taxonomy_name,
                                    $taxonomy_object->labels->singular_name,
                                    $taxonomy_object->labels->name,
                                    strtolower($taxonomy_name),
                                    strtolower($taxonomy_object->labels->singular_name),
                                    strtolower($taxonomy_object->labels->name)
                                );
                                
                                // Prüfe, ob eine der Varianten in den angeforderten Taxonomien ist
                                foreach ($tax_variants as $variant) {
                                    if (in_array($variant, $requested_taxonomies) || in_array(strtolower($variant), $lowercase_requested)) {
                                        $show_taxonomies[] = $taxonomy_name;
                                        break;
                                    }
                                }
                            }
                        } else {
                            // Wenn keine spezifischen Taxonomien angefordert wurden, zeige alle
                            // $show_taxonomies = $taxonomies; // TODO: könnte ich sogar eine admin Setting draus, sodass der user einstellen kann ob default alle kommen sollen oder Default keine
                            $show_taxonomies = array(); 
                        }
                        
                        // Lokale Debug-Ausgabe für die Taxonomien
                        if (filter_var($processed_atts['debug'], FILTER_VALIDATE_BOOLEAN)) {
                            $taxonomies_debug .= '<h4 style="padding: 0; color: #000;">Anzuzeigende Taxonomien:</h4>';
                            $taxonomies_debug .= '<pre style="padding: 0; color: #000;">' . print_r($show_taxonomies, true) . '</pre>';
                            $output .= '<div style="background: #e0f7fa; color: #000; border: 1px solid #00acc1; padding: 10px; margin: 10px 0;">';
                            $output .= $taxonomies_debug;
                            $output .= '</div>';
                        }

                        if (!empty($show_taxonomies)) {
                            foreach ($show_taxonomies as $taxonomy) {
                                $terms = get_the_terms(get_the_ID(), $taxonomy);

                                if (!empty($terms) && !is_wp_error($terms)) {
                                    // Hole das Taxonomie-Objekt, um z. B. den singular_name zu erhalten
                                    $taxonomy_object = get_taxonomy($taxonomy);
                                    $taxonomy_label = isset($taxonomy_object->labels->singular_name) ? $taxonomy_object->labels->singular_name : $taxonomy;
                                    
                                    // Erstelle ein Array mit den Namen der Begriffe
                                    $term_names = array_map(function($term) {
                                        return esc_html($term->name);
                                    }, $terms);

                                    // --> old html structure
                                    // $output .= '<div class="post-taxonomies">';
                                    // $output .= '<strong>' . esc_html($taxonomy_label) . ':</strong> ';
                                    // $output .= '<span class="taxonomy-term">' . implode(', ', $term_names) . '</span>'; // Kommagetrennte Ausgabe
                                    // $output .= '</div>';

                                    // --> new html structure
                                    $output .= '<div class="post-taxonomies">';
                                    // $output .= '<strong>' . esc_html($taxonomy_label) . ':</strong> ';
                                    $output .= '<ul>';
                                    foreach ($terms as $term) {
                                        $output .= '<li class="taxonomy-term">' . esc_html($term->name) . '</li>';
                                    }
                                    $output .= '</ul>';
                                    $output .= '</div>';
                                }
                            }
                        }
                    $output .= '</div>';

                    /**
                     * IMAGE
                     */  
                    // first solustion 
                    // $output .= get_the_post_thumbnail(get_the_ID(), 'full'); // TODO: hier wären noch Performance Optimierungen drin

                    // second try to get valid sizes attribute 
                    // TODO: folgenden Code müsste ich jetzt das sizes attribute je nachdem ob hero_slider="true" oder "false" anders setzen
                    $image_id = get_post_thumbnail_id(get_the_ID());
                    $image_large = wp_get_attachment_image_src($image_id, 'large')[0];
                    $image_full = wp_get_attachment_image_src($image_id, 'full')[0];
                    $alt_text = get_post_meta($image_id, '_wp_attachment_image_alt', true);

                    $output .= '<img src="' . esc_url($image_large) . '" 
                                    srcset="' . esc_url($image_large) . ' 1024w, ' . esc_url($image_full) . ' 1707w" 
                                    sizes="(max-width: 1707px) 100vw, 1707px" 
                                    alt="' . esc_attr($alt_text) . '" 
                                    class="slider-image">';


                $output .= '</div>';
            }

            if(!$imageOnly) { 
                $output .= '<div class="post-text-wrapper">';
                    /* TODO: ggf. je nach Settings hier oder oben im Bild einfügen */
                    /*
                    $output .= '<div class="post-taxonomies-wrapper">';
                    $taxonomies = get_object_taxonomies('videothek');
                    
                    // Überprüfe, ob bestimmte Taxonomien angezeigt werden sollen
                    $show_taxonomies = array();
                    $taxonomies_debug = ''; // Separate Debug-Variable für diese Funktion
                    
                    if (!empty($processed_atts['showtaxonomies'])) {
                        // Trenne bei Komma und entferne Leerzeichen
                        $requested_taxonomies = array_map('trim', explode(',', $processed_atts['showtaxonomies']));
                        
                        // Erstelle ein Array mit Kleinbuchstaben für besseren Vergleich
                        $lowercase_requested = array_map('strtolower', $requested_taxonomies);
                        
                        $taxonomies_debug .= '<h4 style="padding: 0; color: #000;">Angeforderte Taxonomien:</h4>';
                        $taxonomies_debug .= '<pre style="padding: 0; color: #000;">' . print_r($requested_taxonomies, true) . '</pre>';
                        
                        // Prüfe jede verfügbare Taxonomie
                        foreach ($taxonomies as $taxonomy_name) {
                            $taxonomy_object = get_taxonomy($taxonomy_name);
                            $tax_variants = array(
                                $taxonomy_name,
                                $taxonomy_object->labels->singular_name,
                                $taxonomy_object->labels->name,
                                strtolower($taxonomy_name),
                                strtolower($taxonomy_object->labels->singular_name),
                                strtolower($taxonomy_object->labels->name)
                            );
                            
                            // Prüfe, ob eine der Varianten in den angeforderten Taxonomien ist
                            foreach ($tax_variants as $variant) {
                                if (in_array($variant, $requested_taxonomies) || in_array(strtolower($variant), $lowercase_requested)) {
                                    $show_taxonomies[] = $taxonomy_name;
                                    break;
                                }
                            }
                        }
                    } else {
                        // Wenn keine spezifischen Taxonomien angefordert wurden, zeige alle
                        // $show_taxonomies = $taxonomies; // TODO: könnte ich sogar eine admin Setting draus, sodass der user einstellen kann ob default alle kommen sollen oder Default keine
                        $show_taxonomies = array(); 
                    }
                    
                    // Lokale Debug-Ausgabe für die Taxonomien
                    if (filter_var($processed_atts['debug'], FILTER_VALIDATE_BOOLEAN)) {
                        $taxonomies_debug .= '<h4 style="padding: 0; color: #000;">Anzuzeigende Taxonomien:</h4>';
                        $taxonomies_debug .= '<pre style="padding: 0; color: #000;">' . print_r($show_taxonomies, true) . '</pre>';
                        $output .= '<div style="background: #e0f7fa; color: #000; border: 1px solid #00acc1; padding: 10px; margin: 10px 0;">';
                        $output .= $taxonomies_debug;
                        $output .= '</div>';
                    }

                    if (!empty($show_taxonomies)) {
                        foreach ($show_taxonomies as $taxonomy) {
                            $terms = get_the_terms(get_the_ID(), $taxonomy);

                            if (!empty($terms) && !is_wp_error($terms)) {
                                // Hole das Taxonomie-Objekt, um z. B. den singular_name zu erhalten
                                $taxonomy_object = get_taxonomy($taxonomy);
                                $taxonomy_label = isset($taxonomy_object->labels->singular_name) ? $taxonomy_object->labels->singular_name : $taxonomy;
                                
                                // Erstelle ein Array mit den Namen der Begriffe
                                $term_names = array_map(function($term) {
                                    return esc_html($term->name);
                                }, $terms);

                                // --> old html structure
                                // $output .= '<div class="post-taxonomies">';
                                // $output .= '<strong>' . esc_html($taxonomy_label) . ':</strong> ';
                                // $output .= '<span class="taxonomy-term">' . implode(', ', $term_names) . '</span>'; // Kommagetrennte Ausgabe
                                // $output .= '</div>';

                                // --> new html structure
                                $output .= '<div class="post-taxonomies">';
                                // $output .= '<strong>' . esc_html($taxonomy_label) . ':</strong> ';
                                $output .= '<ul>';
                                foreach ($terms as $term) {
                                    $output .= '<li class="taxonomy-term">' . esc_html($term->name) . '</li>';
                                }
                                $output .= '</ul>';
                                $output .= '</div>';
                            }
                        }
                    }
                    $output .= '</div>';
                    */


                    // Beitragstitel
                    $output .= '<div class="post-content-wrapper">';

                        $output .= '<div class="inner-content">';

                            $output .= '<h4 class="post-title">' . get_the_title() . '</h4>';

                            // Datum
                            // $output .= '<div class="post-meta">';
                            // $output .= '<span class="post-date">' . get_the_date() . '</span>';
                            // $output .= '</div>';

                            // Auszug
                            $output .= '<div class="post-content">' . get_the_excerpt() . '</div>';

                            // Weiterlesen-Button
                            $output .= '<div class="post-cta">';
                            $output .= '</div>';

                        $output .= '</div>';

                    $output .= '</div>'; // post-content-wrapper
                $output .= '</div>'; // post-text-wrapper
            }

            $output .= '</article>';
        }

        $output .= '</div>'; // vecura-slide__list
        $output .= '</div>'; // vecura-slide__wrapper-inner

        $output .= '<div class="slider-position-display">
                        <span></span>
                    </div>';

        $output .= '</div>'; // vecura-slide__wrapper
        
        $output .= '</div>'; // vecura-slide
        
        wp_reset_postdata();
    } else {
        $output .= '<p>' . __('Keine Videos gefunden.', 'textdomain') . '</p>';
    }

    // CTA Button nach dem Slider hinzufügen
    if (!empty($processed_atts['cta_text']) && !empty($processed_atts['cta_url'])) {
        $output .= '<div class="videothek-cta-wrapper">';
        $output .= '<a href="' . esc_url($processed_atts['cta_url']) . '" class="videothek-cta-button">' . esc_html($processed_atts['cta_text']) . '</a>';
        $output .= '</div>';
    }


    $output .= '</div>'; // videothek-wrapper

    return $output;
}
add_shortcode('videothek', 'render_videothek_slider');

/**
 * Lade das Slider-Skript für die Videothek
 */
function enqueue_videothek_slider_script() {
    wp_enqueue_script(
        'vecura-slider',
        VECURA_PLUGIN_URL . 'js/slider.js',
        array('jquery'),
        '1.0',
        true
    );

    wp_add_inline_script('vecura-slider', '
        document.addEventListener("DOMContentLoaded", function() { 
            if (typeof initAllSliders === "function") {
                initAllSliders();
            } else {
                console.log("initAllSliders nicht verfügbar - warte auf vollständiges Laden");
                // Fallback-Methode zum verzögerten Aufruf
                setTimeout(function() {
                    if (typeof initAllSliders === "function") {
                        initAllSliders();
                    } else {
                        console.error("initAllSliders konnte nicht gefunden werden");
                    }
                }, 1000);
            }
        });
    ');
}
add_action('wp_enqueue_scripts', 'enqueue_videothek_slider_script');


// echo VECURA_PLUGIN_URL . 'js/slider.js';