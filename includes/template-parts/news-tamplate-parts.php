<?php

/* Lade notwendige Scripts für News */
function enqueue_news_scripts() {
    wp_enqueue_script('masonry');
    wp_enqueue_script('imagesloaded');

    $script_path = plugin_dir_path(__FILE__) . '../../js/news-masonry.js';
    $script_url = plugins_url('../../js/news-masonry.js', __FILE__);
    
    if (file_exists($script_path)) {
        $version = filemtime($script_path);
    } else {
        $version = '1.0';
    }

    wp_enqueue_script('news-masonry', $script_url, array('jquery', 'masonry', 'imagesloaded'), $version, true);
}
add_action('wp_enqueue_scripts', 'enqueue_news_scripts');


/* Lade News-spezifische CSS-Styles */
function get_news_styles() {
    $text_color = get_option('vecura_mediathek_text_color', '#000000');
    echo '<style>
        .news-wrapper {
            margin: 0 auto;
            color: ' . esc_attr($text_color) . ';
            padding: 3rem 0;
        }

        .news-content p {
            margin: 1rem 0 2rem 0;
        }

        .news-wrapper .news-posts {
            margin: 36px 0px;
        }
        .news-wrapper .news-post {
            width: calc(33.333% - (36px * 2 / 3));
            margin: 0 0px 20px 0px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 0px rgba(0,0,0,0.0);
            overflow: hidden;
            transition: box-shadow 300ms ease-in-out;
        }
        .news-wrapper .news-post:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .news-wrapper .post-thumbnail img {
            width: 100%;
            height: auto;
            object-fit: cover;
            transition: transform 300ms ease;
        }
        .news-wrapper .news-post:hover .post-thumbnail img {
            transform: scale(1.05);
        }
        .news-wrapper .post-content-wrapper {
            padding: 1rem;
            display: flex;
            flex-direction: column;
        }
        .news-wrapper .post-title {
            font-size: 1.2em;
        }
        .news-wrapper .post-title a {
            color: #112233;
            text-decoration: none;
        }
        .news-wrapper .post-title a:hover {
            color: #0066cc;
        }
        .news-wrapper .post-meta {
            color: #112233;
            padding: 0.5rem 0;
            font-size: 0.9em;
            display: flex;
            justify-content: space-between;
        }
        .news-wrapper .post-content {
            line-height: 1.6;
        }
        .news-wrapper .read-more {
            display: inline-block;
            margin-top: 15px;
            padding: 8px 16px;
            background-color: #0066cc;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 300ms ease;
        }
        .news-wrapper .read-more:hover {
            background-color: #0052a3;
        }


        /* Für mittlere Bildschirme: 2 Spalten */
        @media (max-width: 992px) {
            .news-wrapper .news-post  {
                width: calc(50% - 18px); /* Gutter eventuell anpassen */
            }
        }

        /* Für kleine Bildschirme: 1 Spalte */
        @media (max-width: 576px) {
            .news-wrapper .news-post  {
                width: 100%;
            }
        }

        /* Styling für den Mehr laden Button */
        .load-more-news {
            display: block;
            margin: 2rem auto;
            padding: 12px 24px;
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 1em;
            cursor: pointer;
            transition: background-color 300ms ease, transform 150ms ease;
        }

        .load-more-news:hover {
            background-color: #0052a3;
            transform: translateY(-1px);
        }

        .load-more-news:active {
            transform: translateY(0);
        }
    </style>';
}
add_action('wp_head', 'get_news_styles');



/* Shortcode zur Darstellung von News */
function render_news_posts($atts) {
    // Abrufen, ob News als CPT oder als Posts genutzt werden sollen
    $news_source = get_option('vecura_mediathek_news_source', 'cpt');
    $post_type = ($news_source === 'posts') ? 'post' : 'news';

    // Standardwerte für den Shortcode (inkl. `count`)
    $atts = shortcode_atts(array(
        'headline' => 'Neueste News',
        'tagline'  => '',
        'content'  => '',
        'id'       => '',
        'count'    => 6, // Anzahl der initial geladenen Posts
    ), $atts, 'news');

    // Query für die News-Beiträge
    $args = array(
        'post_type'      => $post_type,
        'posts_per_page' => intval($atts['count']),
        'paged'          => 1
    );

    $query = new WP_Query($args);
    $max_pages = $query->max_num_pages;

    $output = '<div class="news-wrapper" ' . (!empty($atts['id']) ? ' id="' . esc_attr($atts['id']) . '"' : '') . '>';

    if (!empty($atts['tagline'])) {
        $output .= '<h3 class="news-tagline vecura-mediahub-shortcode-tagline">' . esc_html($atts['tagline']) . '</h3>';
    }

    if (!empty($atts['headline'])) {
        $output .= '<h2 class="news-headline vecura-mediahub-shortcode-headline">' . esc_html($atts['headline']) . '</h2>';
    }

    if (!empty($atts['content'])) {
        $output .= '<div class="news-content vecura-mediahub-shortcode-content"><p>' . wp_kses_post($atts['content']) . '</p></div>';
    }

    // Generiere eine eindeutige ID für diesen Shortcode
    $unique_id = !empty($atts['id']) ? $atts['id'] : 'news-' . uniqid();

    $output .= '<div class="news-container" data-container-id="' . esc_attr($unique_id) . '">';
    // $output .= '<div class="news-posts">';
    
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $output .= render_single_news_post();
        }
        wp_reset_postdata();
    } else {
        $output .= '<p>' . __('Keine News gefunden.', 'textdomain') . '</p>';
    }

    // $output .= '</div>'; // news-posts
    $output .= '</div>'; // news-container

    // "Mehr laden"-Button hinzufügen, wenn es weitere Seiten gibt
    if ($max_pages > 1) {
        $output .= '<button class="load-more-news" data-container-id="' . esc_attr($unique_id) . '" data-page="1" data-max="' . $max_pages . '" data-count="' . intval($atts['count']) . '">'
                    . __('Mehr laden', 'textdomain') . 
                   '</button>';
    }

    $output .= '</div>'; // news-wrapper

    return $output;
}
add_shortcode('news', 'render_news_posts');


function render_single_news_post() {
    ob_start();

    echo '<article class="news-post">';
    
    // Beitragsbild
    if (has_post_thumbnail()) {
        echo '<div class="post-thumbnail">';
        echo '<a href="' . get_permalink() . '">';
        echo get_the_post_thumbnail(get_the_ID(), 'large');
        echo '</a>';
        echo '</div>';
    }

    echo '<div class="post-content-wrapper">';
    echo '<h4 class="post-title"><a href="' . get_permalink() . '">' . get_the_title() . '</a></h4>';

    // Datum
    echo '<div class="post-meta">';
    echo '<span class="post-date">' . get_the_date() . '</span>';
    echo '</div>';

    // Auszug
    echo '<div class="post-content">' . get_the_excerpt() . '</div>';

    // Weiterlesen-Button
    echo '<div class="post-cta">';
    echo '<a href="' . get_permalink() . '" class="read-more">' . __('Weiterlesen', 'textdomain') . '</a>';
    echo '</div>';

    echo '</div>'; // post-content-wrapper
    echo '</article>';

    return ob_get_clean();
}


function load_more_news() {
    check_ajax_referer('vecura_mediathek_nonce', 'nonce');

    $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
    $count = isset($_POST['count']) ? intval($_POST['count']) : 5;

    $news_source = get_option('vecura_mediathek_news_source', 'cpt');
    $post_type = ($news_source === 'posts') ? 'post' : 'news';

    $args = array(
        'post_type'      => $post_type,
        'posts_per_page' => $count,
        'paged'          => $page,
    );

    $query = new WP_Query($args);

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            echo render_single_news_post();
        }
        wp_reset_postdata();
    }

    wp_die();
}
add_action('wp_ajax_load_more_news', 'load_more_news');
add_action('wp_ajax_nopriv_load_more_news', 'load_more_news');


function enqueue_news_loadmore_script() {
    $script_path = plugin_dir_path(__FILE__) . '../../js/news-loadmore.js';
    $script_url  = plugins_url('../../js/news-loadmore.js', __FILE__);

    // Erhalte die letzte Änderungszeit der Datei als Version
    $script_version = file_exists($script_path) ? filemtime($script_path) : '1.0';

    wp_enqueue_script(
        'news-loadmore',
        $script_url,
        array('jquery'),
        $script_version, // ✅ Nutze die Dateizeit als Versionsnummer
        true
    );

    wp_localize_script('news-loadmore', 'news_ajax_object', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce'    => wp_create_nonce('vecura_mediathek_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'enqueue_news_loadmore_script');
