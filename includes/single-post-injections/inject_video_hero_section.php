<?php
function enqueue_video_lightbox_script() {
    if ( ! is_singular( 'videothek' ) ) {
        return;
    }
    
    // Definiere Pfad und URL zur JS-Datei mithilfe der globalen Konstanten:
    $script_path = MY_PLUGIN_ROOT . 'js/video-lightbox.js';
    $script_url  = VECURA_PLUGIN_URL . 'js/video-lightbox.js';
    // Nutze filemtime(), um bei Änderungen automatisch eine neue Versionsnummer zu generieren:
    $version = file_exists( $script_path ) ? filemtime( $script_path ) : '1.0';

    // Hole die relevanten Meta-Daten vom aktuellen Post
    $post_id            = get_queried_object_id();
    if ( ! $post_id ) {
        return;
    }
    $video_headline     = get_post_meta( $post_id, 'video_headline', true );
    $use_youtube_frame  = get_post_meta( $post_id, 'use_youtube_frame', true );
    $youtube_video_url  = get_post_meta( $post_id, 'youtube_video_url', true );
    $selected_video_id  = get_post_meta( $post_id, 'selected_video_id', true );
    // Ermittele die URL des self-hosted Videos anhand der Attachment-ID:
    $self_hosted_video  = wp_get_attachment_url( $selected_video_id );

    // Erzeuge das videoData-Objekt, das alle Video-Daten zusammenfasst:
    $videoData = array(
        'useYoutubeFrame' => $use_youtube_frame,  // "1" = YouTube, "0" = self-hosted
        'youtubeVideoUrl' => $youtube_video_url,  // Bei YouTube: die Video-ID
        'videoUrl'        => $self_hosted_video,   // URL des selbstgehosteten Videos
        'videoHeadline'   => $video_headline
    );

    // Registriere und enqueue das Script:
    wp_register_script(
        'video-lightbox-script',
        $script_url,
        array(), // Hier kannst Du z. B. ['jquery'] angeben, falls benötigt
        $version,
        true    // Script wird im Footer geladen
    );
    
    // Übergib das videoData-Objekt an das Script:
    wp_localize_script( 'video-lightbox-script', 'videoData', $videoData );
    wp_enqueue_script( 'video-lightbox-script' );
}
add_action( 'wp_enqueue_scripts', 'enqueue_video_lightbox_script' );


function inject_video_hero_section() {
    if (!is_singular('videothek')) {
        return;
    }

    // Hole alle relevanten Meta-Daten
    $post_id = get_queried_object_id();
    if ( ! $post_id ) {
        echo '<p>Kein Post gefunden.</p>';
        return;
    }

    $video_headline    = get_post_meta($post_id, 'video_headline', true);

    $use_youtube_frame = get_post_meta($post_id, 'use_youtube_frame', true);
    $youtube_video_url = get_post_meta($post_id, 'youtube_video_url', true);

    // Richtig: $selected_video_id anhand von $post_id abrufen
    $selected_video_id = get_post_meta($post_id, 'selected_video_id', true);
    // Self-hosted Video URL ermitteln:
    $self_hosted_video = wp_get_attachment_url($selected_video_id);

    // ob_start(function($buffer) {
    ob_start(function($buffer) use ($post_id, $use_youtube_frame, $youtube_video_url, $selected_video_id, $video_headline, $self_hosted_video) {
        // Page Builder erkennen
        $active_builder = detect_active_page_builder();

        /**
         * Je nach gefundenem Page Builder könnten hier unterschiedliche patterns benutzt werden 
         * TODO: Ich sollte auch noch in den Admin Settings die Option ermöglichen eigene Pattern anzugeben 
         */
        $patterns = [
            '<div class="container-wrap"', // WP-Backery (at least V.D.)
            '<main id="content" class="site-content"',
            '<main id="content"',
            '<main id="main"',
            '<main class="',
            '<main>',
            '<div id="main"',
            '<div id="content"'
        ];

        

        foreach ($patterns as $pattern) {
            $pos = strpos($buffer, $pattern);
            if ($pos !== false) {
                // Finde das schließende > des main Tags
                $closing_pos = strpos($buffer, '>', $pos);
                if ($closing_pos !== false) {

                    $used_pattern = htmlspecialchars($pattern); // Speichere das gefundene Pattern

                    
                    

                    // Hero HTML erstellen
                    $hero = '<div class="social-wall-hero-section">';
                    $hero .= '<div class="social-wall-hero-container">';

                        // Left side
                        $hero .= '<div class="left-side">';
                            // Beitragsbild hinzufügen
                            if (has_post_thumbnail()) {
                                $hero .= '<div class="featured-image">';
                                $hero .=    '<button class="play-video">
                                                <svg class="play-icon" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                    <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd"/>
                                                </svg>
                                            </button>';
                                $hero .= get_the_post_thumbnail(null, 'large', array('class' => 'hero-image'));
                                $hero .= '</div>';
                            }
                        $hero .= '</div>';

                        $hero .= '<div class="right-side">';
                            $hero .= '<h1>' . get_the_title() . '</h1>';
                            // $hero .= '<p>active_builder!: ' . $active_builder . '</p>';
                            // $hero .= '<p><strong>Verwendetes Pattern:</strong> ' . esc_html($used_pattern) . '</p>'; // Hier anzeigen

                            /** Create style for main/content color */
                            $selector = html_pattern_to_css_selector($pattern);
                            // $hero .= '<p><strong>CSS Selector:</strong> ' . $selector . '</p>';

                            $text_color = get_option('vecura_mediathek_text_color', '#000000');
                            $background_color = get_option('vecura_mediathek_background_color', '#000000');

                            $hero .= '<style>
                                        body, ' . $selector . ',' . $selector . ' p {
                                            color: ' . $text_color . ';
                                            background: ' . $background_color . ' !important;
                                        }
                                        </style>';

                            // Excerpt hinzufügen
                            $hero .= '<div class="excerpt-text">' . get_the_excerpt() . '</div>';

                            $hero .=    '<button class="content-play-button">
                                            <span class="button-text">Abspielen</span>
                                            <svg class="play-icon" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd"/>
                                            </svg>
                                        </button>';

                            
                            // Taxonomien
                            $taxonomies = get_object_taxonomies('social_wall', 'objects');
                            foreach ($taxonomies as $taxonomy) {
                                $terms = get_the_terms(get_the_ID(), $taxonomy->name);
                                if ($terms && !is_wp_error($terms)) {
                                    $hero .= '<div class="taxonomy-' . esc_attr($taxonomy->name) . '">';
                                    $hero .= '<strong>' . esc_html($taxonomy->labels->singular_name) . ':</strong> ';
                                    $term_names = wp_list_pluck($terms, 'name');
                                    $hero .= esc_html(implode(', ', $term_names));
                                    $hero .= '</div>';
                                }
                            }


                            // Video-Daten ausgeben
                            // Debug-Ausgabe der Meta-Daten
                            /*
                            $hero .= '<pre style="background: #112233; color: #fff; padding: 1rem;">';
                            $hero .= 'Post ID: ' . $post_id . "\n";
                            $hero .= 'use_youtube_frame: ' . $use_youtube_frame . "\n";
                            $hero .= 'youtube_video_url: ' . $youtube_video_url . "\n";
                            $hero .= 'selected_video_id: ' . $selected_video_id . "\n";
                            $hero .= 'video_headline: ' . $video_headline . "\n";
                            $hero .= 'self_hosted_video: ' . $self_hosted_video . "\n";
                            $hero .= '</pre>';
                            */

                            /*
                            if ($use_youtube_frame == '1' && !empty($youtube_video_url)) {
                                $hero .= '<div class="video-embed">';
                                $hero .= '<h2>' . esc_html($video_headline) . '</h2>';
                                $hero .= '<iframe width="560" height="315" src="https://www.youtube.com/embed/' . esc_attr($youtube_video_url) . '" frameborder="0" allowfullscreen></iframe>';
                                $hero .= '</div>';
                            } elseif (!empty($self_hosted_video)) {
                                $hero .= '<div class="video-embed">';
                                $hero .= '<h2>' . esc_html($video_headline) . '</h2>';
                                $hero .= '<video width="100%" controls><source src="' . esc_url($self_hosted_video) . '" type="video/mp4"></video>';
                                $hero .= '</div>';
                            } else {
                                $hero .= '<p>' . __('Kein Video ausgewählt.', 'textdomain') . '</p>';
                            }
                            */


                        $hero .= '</div>';

                    $hero .= '</div>'; // Ende .social-wall-hero-container
                    $hero .= '</div>'; // Ende .social-wall-hero-section


                    // $video_url = get_post_meta(get_the_ID(), '_videothek_video_url', true);
                    // if ($video_url) {
                    //     $hero .= '<video width="100%" controls><source src="' . esc_url($video_url) . '" type="video/mp4"></video>';
                    // } else {
                    //     $hero .= '<p>' . __('Kein Video ausgewählt.', 'textdomain') . '</p>';
                    // }

                        
                    // Füge den kompletten main Tag mit Hero ein
                    $buffer = substr_replace(
                        $buffer, 
                        substr($buffer, $pos, $closing_pos - $pos + 1) . $hero,
                        $pos,
                        $closing_pos - $pos + 1
                    );
                    break;

                }
            }
        }
        
        return $buffer;
    });
}
add_action('template_redirect', 'inject_video_hero_section');



// Styles bleiben gleich
function add_videothek_styles() {
    if (!is_singular('videothek')) {
        return;
    }
    
    $text_color = get_option('vecura_mediathek_text_color', '#000000');
    $accent_color_primary = get_option('vecura_mediathek_accent_color_primary', '#000000');
    $accent_color_secondary = get_option('vecura_mediathek_accent_color_secondary', '#000000');
    $accent_color_tertiary = get_option('vecura_mediathek_accent_color_tertiary', '#000000');
    $background_color = get_option('vecura_mediathek_background_color', '#000000');
    ?>

    <style>
        .social-wall-hero-section {
            width: 100%;
            background: <?php echo esc_attr($background_color) ?>;
            color: <?php echo esc_attr($text_color) ?>;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        .social-wall-hero-container {
            display: flex;
            justify: between;
            align-items: center;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
            width: 100%;
            max-width: 1800px;
            margin: 0 auto;
            padding: 0 70px;
        }

        .social-wall-hero-container h1,
        .social-wall-hero-container .excerpt-text {
            text-wrap: balance;
        }

        button.content-play-button {
            border-radius: 99px;
            padding: 0.5rem 1rem;
            margin: 1rem 0;
            border: none;
            background: #fff;
            display: flex;
            gap: 6px;
            font-size: 1.2rem;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.24s ease-in-out;
        }
        button.content-play-button:hover {
            background: <?php echo esc_attr($accent_color_primary) ?>;
            color: #fff;
        }

        /** Image & play icon */
        .featured-image {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
        }
        .featured-image button.play-video {
            cursor: pointer;
            border-radius: 50%;
            aspect-ratio: 1 / 1;
            border: none;
            position: absolute;
            width: 25%;
            background: rgba(255, 255, 255, 0.5);
            transition: all 0.24s ease-in-out;
        }
        .featured-image button.play-video .play-icon {
            width: 66%;
            height: 66%;
            transition: all 0.24s ease-in-out;
            color: <?php echo esc_attr($background_color) ?>;
        }
        /* Hover */ 
        .featured-image:hover button.play-video {
            border-radius: 50%;
            aspect-ratio: 1 / 1;
            border: none;
            position: absolute;
            width: 22.5%;
            background: rgba(255, 255, 255, 0.6);
        }
        .featured-image:hover button.play-video .play-icon {
            /* width: 90%;
            height: 90%; */
            color: <?php echo esc_attr($accent_color_primary) ?>;
        }
        .social-wall-hero-container .featured-image img {
            aspect-ratio: 3 / 4;
            object-fit: cover;
            border-radius: 1rem;
        }


        .social-wall-hero-section h1 {
            margin: 0 0 20px 0;
            font-size: 2.5em;
            line-height: 1.2;
        }
        .taxonomy-wrapper {
            margin-top: 20px;
        }
        @media (max-width: 768px) {
            .social-wall-hero-section {
                padding: 40px 0;
            }
            .social-wall-hero-section h1 {
                font-size: 2em;
            }
        }


        /* Video Modal */ 
        .video-modal {
            background: rgb(0 0 0 / 80%);
            background: rgb(2 45 51 / 66%);
            padding: 0px 5vw 0px 5vw;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            top: 0;
            left: 0;
            position: fixed;
            height: 0;
            width: 0;
            z-index: 99999;
            overflow: hidden;
            opacity: 0;
            backdrop-filter: blur(0px);
            pointer-events: none;

            transition: opacity 600ms ease-in-out, backdrop-filter 600ms ease-in-out;
        }
        .video-modal-close-button {
            margin-left: auto;
            color: #fff;
            padding: 1rem 0;
            cursor: pointer;
        }

        .video-modal.active {
            top: 0;
            left: 0;
            position: fixed;
            height: 100vh;
            width: 100vw;
            z-index: 99999;
            backdrop-filter: blur(8px);
            opacity: 1;
            pointer-events: all;
        }

        .video-modal video {
            max-width: 100%;
            max-height: 85vh;
        }

        .video-modal iframe {
            width: 100% !important;
            height: auto !important;
            max-height: 90vh !important;
            aspect-ratio: 16 / 9 !important;
        }

        iframe.iframe-embed, iframe {
            max-width: 100%;
        }
    </style>
    <?php
}
add_action('wp_head', 'add_videothek_styles');