<?php
function inject_social_wall_hero_section() {
    if (!is_singular('social_wall')) {
        return;
    }

    ob_start(function($buffer) {
        // Page Builder erkennen
        $active_builder = detect_active_page_builder();

        /**
         * Je nach gefundenem Page Builder könnten hier unterschiedliche patterns benutzt werden 
         * TODO: Ich sollte auch noch in den Admin Settings die Option ermöglichen eigene Pattern anzugeben 
         */
        $patterns = [
            '<div class="container-wrap"', // WP-Backery (at least V.D.)
            '<main id="content" class="site-content"',
            '<main id="content"',
            '<main id="main"',
            '<main class="',
            '<main>',
            '<div id="main"',
            '<div id="content"'
        ];

        

        foreach ($patterns as $pattern) {
            $pos = strpos($buffer, $pattern);
            if ($pos !== false) {
                // Finde das schließende > des main Tags
                $closing_pos = strpos($buffer, '>', $pos);
                if ($closing_pos !== false) {

                    $used_pattern = htmlspecialchars($pattern); // Speichere das gefundene Pattern

                    /** Create style for main/content color */
                    $selector = html_pattern_to_css_selector($pattern);
                    // $hero .= '<p><strong>CSS Selector:</strong> ' . $selector . '</p>';

                    $text_color = get_option('vecura_mediathek_text_color', '#000000');
                    $background_color = get_option('vecura_mediathek_background_color', '#000000');

                    $hero = '<style>
                                body, ' . $selector . ',' . $selector . ' p {
                                    color: ' . $text_color . ';
                                    background: ' . $background_color . ' !important;
                                }
                                </style>';

                    // Hero HTML erstellen
                    $hero .= '<div class="social-wall-hero-section">';
                    $hero .= '<div class="social-wall-hero-container">';

                        // Left side
                        $hero .= '<div class="left-side">';
                            // Beitragsbild hinzufügen
                            if (has_post_thumbnail()) {
                                $hero .= '<div class="featured-image">';
                                $hero .= get_the_post_thumbnail(null, 'large', array('class' => 'hero-image'));
                                $hero .= '</div>';
                            }
                        $hero .= '</div>';

                        $hero .= '<div class="right-side">';
                            $hero .= '<h1>' . get_the_title() . '</h1>';
                            // $hero .= '<p>active_builder: ' . $active_builder . '</p>';
                            // $hero .= '<p><strong>Verwendetes Pattern:</strong> ' . esc_html($used_pattern) . '</p>'; // Hier anzeigen

                            // Excerpt hinzufügen
                            $hero .= '<div class="excerpt-text">' . get_the_excerpt() . '</div>';
                            
                            // Taxonomien
                            $taxonomies = get_object_taxonomies('social_wall', 'objects');
                            foreach ($taxonomies as $taxonomy) {
                                $terms = get_the_terms(get_the_ID(), $taxonomy->name);
                                if ($terms && !is_wp_error($terms)) {
                                    $hero .= '<div class="taxonomy-' . esc_attr($taxonomy->name) . '">';
                                    $hero .= '<strong>' . esc_html($taxonomy->labels->singular_name) . ':</strong> ';
                                    $term_names = wp_list_pluck($terms, 'name');
                                    $hero .= esc_html(implode(', ', $term_names));
                                    $hero .= '</div>';
                                }
                            }

                        $hero .= '</div>';

                    $hero .= '</div>'; // Ende .social-wall-hero-container
                    $hero .= '</div>'; // Ende .social-wall-hero-section
                        
                    // Füge den kompletten main Tag mit Hero ein
                    $buffer = substr_replace(
                        $buffer, 
                        substr($buffer, $pos, $closing_pos - $pos + 1) . $hero,
                        $pos,
                        $closing_pos - $pos + 1
                    );
                    break;

                }
            }
        }
        
        return $buffer;
    });
}
add_action('template_redirect', 'inject_social_wall_hero_section');



// Styles bleiben gleich
function add_social_wall_styles() {
    if (!is_singular('social_wall')) {
        return;
    }
    
    $text_color = get_option('vecura_mediathek_text_color', '#000000');
    $accent_color_primary = get_option('vecura_mediathek_accent_color_primary', '#000000');
    $accent_color_secondary = get_option('vecura_mediathek_accent_color_secondary', '#000000');
    $accent_color_tertiary = get_option('vecura_mediathek_accent_color_tertiary', '#000000');
    $background_color = get_option('vecura_mediathek_background_color', '#000000');
    ?>

    <style>
        .social-wall-hero-section {
            width: 100%;
            background: <?php echo esc_attr($background_color) ?>;
            color: <?php echo esc_attr($text_color) ?>;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        .social-wall-hero-container {
            display: flex;
            justify: between;
            align-items: center;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
            width: 100%;
            max-width: 1800px;
            margin: 0 auto;
            padding: 0 70px;
        }

        .social-wall-hero-container h1,
        .social-wall-hero-container .excerpt-text {
            text-wrap: balance;
        }
        .social-wall-hero-container .featured-image img {
            aspect-ratio: 3 / 4;
            object-fit: cover;
            border-radius: 1rem;
        }

        .social-wall-hero-section h1 {
            margin: 0 0 20px 0;
            font-size: 2.5em;
            line-height: 1.2;
        }
        .taxonomy-wrapper {
            margin-top: 20px;
        }
        @media (max-width: 768px) {
            .social-wall-hero-section {
                padding: 40px 0;
            }
            .social-wall-hero-section h1 {
                font-size: 2em;
            }
        }
    </style>
    <?php
}
add_action('wp_head', 'add_social_wall_styles');



// TODO: gehört hier eigentlich nicht hin - in andere Datei auslagern 
/** load archive pages */
// Im Plugin-Hauptverzeichnis
function social_wall_archive_template($archive_template) {
    if (is_post_type_archive('social_wall')) {
        // return plugin_dir_path(__FILE__) . 'templates/archive-social-wall.php';
        return MY_PLUGIN_ROOT . 'templates/archive-social-wall.php';
    }
    return $archive_template;
}
add_filter('archive_template', 'social_wall_archive_template');