/*
document.addEventListener("DOMContentLoaded", function () {
	console.log("✅ Masonry Skript wird ausgeführt.")

	var grid = document.querySelector(".news-posts")
	if (grid) {
		var msnry = new Masonry(grid, {
			itemSelector: ".news-post",
			columnWidth: ".news-post",
			percentPosition: true,
			gutter: 20,
		})

		// Bilder erst laden, bevor Masonry das Layout anpasst
		imagesLoaded(grid, function () {
			console.log("📸 Alle Bilder geladen – Masonry aktualisieren")
			msnry.layout()
		})
	} else {
		console.warn("⚠️ .news-posts nicht gefunden, Masonry wurde nicht ausgeführt.")
	}
})
*/


/** Wordks good but without resize event */
/*
document.addEventListener("DOMContentLoaded", function () {
	const buttons = document.querySelectorAll("#load-more-news")
	const newsContainers = document.querySelectorAll(".news-container")

	if (!buttons.length || !newsContainers.length) return

	// Für jeden Container Masonry initialisieren
	newsContainers.forEach((container) => {
		let msnry = new Masonry(container, {
			itemSelector: ".news-post",
			columnWidth: ".news-post",
			percentPosition: true,
			gutter: 20,
			transitionDuration: "0.3s",
		})

		// Bilder erst laden, bevor Masonry das Layout anpasst
		imagesLoaded(container, function () {
			console.log("📸 Alle Bilder geladen – Masonry aktualisieren")
			msnry.layout()
		})

		// 🏗️ Event-Listener für nachgeladene News-Elemente
		document.addEventListener("newsLoaded", function () {
			console.log("🆕 Neue News geladen – Masonry aktualisieren")
			msnry.layout()
		})
	})
})
*/


document.addEventListener("DOMContentLoaded", function () {
	const buttons = document.querySelectorAll("#load-more-news")
	const newsContainers = document.querySelectorAll(".news-container")

	if (!buttons.length || !newsContainers.length) return

	// Array zum Speichern der Masonry-Instanzen
	const masonryInstances = []

	// Für jeden Container Masonry initialisieren und Instanz speichern
	newsContainers.forEach((container) => {
		let msnry = new Masonry(container, {
			itemSelector: ".news-post",
			columnWidth: ".news-post",
			percentPosition: true,
			gutter: 20,
			transitionDuration: "0.3s",
		})
		masonryInstances.push(msnry)

		// Bilder erst laden, bevor Masonry das Layout anpasst
		imagesLoaded(container, function () {
			console.log("📸 Alle Bilder geladen – Masonry aktualisieren")
			msnry.layout()
		})
	})

	// Globaler Event-Listener für nachgeladene News-Elemente
	document.addEventListener("newsLoaded", function () {
		console.log("🆕 Neue News geladen – Masonry aktualisieren")
		masonryInstances.forEach((instance) => {
			instance.layout()
		})
	})

	// Resize-Handler, der alle Masonry-Instanzen aktualisiert
	$(window).resize(function () {
		masonryInstances.forEach((instance) => {
			instance.layout()
		})
	})
})
