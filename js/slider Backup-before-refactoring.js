/**
 * <PERSON><PERSON>t for slider
 */

// Definiere Funktionen außerhalb der Schleife für eine bessere Wiederverwendbarkeit und Testbarkeit
function splitValueUnit(value) {
	const regex = /^(\d+)(\D+)$/
	const match = value.match(regex)

	if (match) {
		return {
			number: parseFloat(match[1]),
			unit: match[2],
		}
	}

	throw new Error("Invalid format")
}

function initAllSliders() {
    console.log('init all sliders')

	const sliderLinks = document.querySelectorAll("[data-link-url]")

	const timeTrashhold = 200
	const mouseMoveTrashhold = 8

	sliderLinks.forEach((link) => {
		// console.log('link: ', link);
		// console.log(link.getAttribute('data-link-url'));

		let isDragging = false
		let startTime
		let startX
		let startY

		link.addEventListener("mousedown", (e) => {
			isDragging = false
			startTime = new Date().getTime()
			startX = e.clientX
			startY = e.clientY
		})

		link.addEventListener("mousemove", (e) => {
			if (Math.abs(e.clientX - startX) > mouseMoveTrashhold || Math.abs(e.clientY - startY) > mouseMoveTrashhold) {
				isDragging = true
			}
		})

		link.addEventListener("mouseup", (e) => {
			const endTime = new Date().getTime()
			const timeDiff = endTime - startTime

			if (!isDragging && timeDiff < timeTrashhold) {
				const linkUrl = link.getAttribute("data-link-url")
				console.log("linkUrl: ", linkUrl)

				const linkTarget = link.getAttribute("data-link-target")
				if (linkTarget === "_blank") {
					window.open(linkUrl, "_blank")
				} else {
					window.location.href = linkUrl
				}
			}
		})

		link.addEventListener("touchstart", (e) => {
			isDragging = false
			startTime = new Date().getTime()
			startX = e.touches[0].clientX
			startY = e.touches[0].clientY
		})

		link.addEventListener("touchmove", (e) => {
			if (Math.abs(e.touches[0].clientX - startX) > mouseMoveTrashhold || Math.abs(e.touches[0].clientY - startY) > mouseMoveTrashhold) {
				isDragging = true
			}
		})

		link.addEventListener("touchend", (e) => {
			const endTime = new Date().getTime()
			const timeDiff = endTime - startTime

			if (!isDragging && timeDiff < timeTrashhold) {
				const linkUrl = link.getAttribute("data-link-url")
				const linkTarget = link.getAttribute("data-link-target")
				if (linkTarget === "_blank") {
					window.open(linkUrl, "_blank")
				} else {
					window.location.href = linkUrl
				}
			}
		})
	})

	const vecuraSlide = document.querySelectorAll(".vecura-slide")
	console.log("vecuraSlide: ", vecuraSlide)

	vecuraSlide.forEach((slide) => {
		console.log("slide: ", slide)

		const vecuraSlideWrapper = slide.querySelector(".vecura-slide__wrapper")
		const vecuraSlideList = slide.querySelector(".vecura-slide__list")
		const vecuraSlideItem = slide.querySelectorAll(".vecura-slide__item")

		// arrows
		const arrowWrapper = slide.querySelector(".arrow-wrapper")
		const leftArrow = arrowWrapper.querySelector(".left")
		const rightArrow = arrowWrapper.querySelector(".right")

		// slider position display
		const sliderPositionDisplay = slide.querySelector(".slider-position-display")
		// const totalSlides = vecuraSlideItem.length - getMaxSlidesInView() + 1 // oder passe diese Berechnung an deine Logik an
        const maxSlides = vecuraSlideItem.length
		console.log("maxSlides: ", maxSlides)
        const totalSlides = slide.getAttribute("data-hero-slider") === "true" ? maxSlides - 1 : maxSlides - getMaxSlidesInView() + 1

		let slideIndicatorAttribute = slide.getAttribute("data-slide-indicator")

		let currentSlide = 0
		let arrayOfSlideValues = []
		let currentTransformX = 0
		let maxSlidesInView = null
		let sliderIndicatorItems
		let sliderAggregator = 1 // is for snapping on dnd (1 snaps on each slide)
		let dragThreshold = 0.25

		function getMaxSlidesInView() {
            const isHeroSlider = slide.getAttribute("data-hero-slider") === "true"
            if (isHeroSlider) {
                return 1
            }

			const mobileMaxSlidesInView = 2 // 1
			const tabletMaxSlidesInView = 2 // 2
			const desktopMaxSlidesInView = parseInt(slide.getAttribute("data-slide-max-slides") || 4) // 3

			console.log("desktopMaxSlidesInView: ", desktopMaxSlidesInView)
			return window.innerWidth <= 699 ? mobileMaxSlidesInView : window.innerWidth <= 999 ? tabletMaxSlidesInView : desktopMaxSlidesInView
		}

		function getSliderAggregator() {
			let dataSlideAggregatorAttribute = parseInt(slide.getAttribute("data-side-aggregation")) || 1
			if (maxSlidesInView > 2) {
				sliderAggregator = dataSlideAggregatorAttribute
			}
			if (maxSlidesInView === 2 && dataSlideAggregatorAttribute > 1) {
				sliderAggregator = 2
			}
			if (maxSlidesInView === 1 && dataSlideAggregatorAttribute > 1) {
				sliderAggregator = 1
			}

			return sliderAggregator
		}

		function getdDragThreshold() {
			// dragThreshold = parseFloat(slide.getAttribute('data-drag-threshold')) || 0.25;
			if (maxSlidesInView === 1) {
				dragThreshold = 0.33
			}
			if (maxSlidesInView === 2 || maxSlidesInView === 3) {
				dragThreshold = 0.25
			}
			if (maxSlidesInView > 3) {
				dragThreshold = 0.1
			}

			return dragThreshold
		}

		function calculateSliderValues() {
			maxSlidesInView = getMaxSlidesInView()
			sliderAggregator = getSliderAggregator()
			dragThreshold = getdDragThreshold()

			if (currentSlide > maxSlides - maxSlidesInView) currentSlide = maxSlides - maxSlidesInView

			let gapCount = maxSlidesInView - 1
			let slideGapAttribute = slide.getAttribute("data-slide-gap")
			console.log("slideIndicatorAttribute: ", slideIndicatorAttribute)
			let slideGap = splitValueUnit(slideGapAttribute)
			let slideFraction = 100 / maxSlidesInView
			let sliderWrapperWidth = vecuraSlideWrapper.offsetWidth
			let slideFractionInPx = (sliderWrapperWidth - gapCount * slideGap.number) / maxSlidesInView
			let minusSliderWidth = (slideGap.number * gapCount) / maxSlidesInView
			let sliderWidth = `calc(${slideFraction}% - ${minusSliderWidth}px)`

			vecuraSlideItem.forEach((slideItem) => {
				slideItem.style.minWidth = sliderWidth
				slideItem.style.marginRight = `${slideGap.number}${slideGap.unit}`
			})

			arrayOfSlideValues = []
			for (let i = 0; i <= maxSlides - maxSlidesInView; i++) {
				let tempCurrentTransformX = -slideFractionInPx * i - slideGap.number * i
				arrayOfSlideValues.push(tempCurrentTransformX)
			}
		}

		const initConrols = () => {
            console.log('initControls')
            
            if (slideIndicatorAttribute === "position_display") {
				sliderPositionDisplay.style.display = "block"
            }
            
			if (currentSlide > totalSlides) {
				rightArrow.classList.add("inactive")
			} else {
				// rightArrow.style.opacity = 1
				rightArrow.classList.remove("inactive")
			}
		}
		initConrols()

		// Update arrows, slider indicators and slider position display
		const updateControls = () => {
			console.log("updateControls called")
			// console.log("slideIndicatorAttribute: ", slideIndicatorAttribute)
			if (slideIndicatorAttribute === "indicators") {
				console.log('slideIndicatorAttribute === "indicators"')
				setIndicatorActive()
			}
			if (slideIndicatorAttribute === "position_display") {
				sliderPositionDisplay.style.display = "block"
				updateSliderPositionDisplay(currentSlide, totalSlides, sliderPositionDisplay)
			}

			// Falls hero_slider aktiv ist, erzwinge, dass die Pfeile aktiv bleiben
			if (slide.getAttribute("data-hero-slider") === "true") {
				leftArrow.classList.remove("inactive")
				rightArrow.classList.remove("inactive")
				// Eventuell kannst Du hier auch spezielle Logik einbauen, falls Du trotz 1 Slide navigieren möchtest.
				return
			}

			if (currentSlide <= 0) {
				// const debugLogs = {
				// 	trueOrFalse: true,
				// 	currentSlide,
				// 	condition: "currentSlide <= 1",
				// }
				// console.table(debugLogs)
				leftArrow.classList.add("inactive")
			} else {
				// const debugLogs = {
				// 	trueOrFalse: false,
				// 	currentSlide,
				// 	condition: "currentSlide <= 1",
				// }
				// console.table(debugLogs)
				leftArrow.classList.remove("inactive")
			}

			if (currentSlide >= maxSlides - totalSlides - 1) {
				// const debugLogs = {
				// 	trueOrFalse: true,
				// 	currentSlide,
				// 	maxSlides,
				// 	condition: "currentSlide >= maxSlides - totalSlides - 1",
				// }
				// console.table(debugLogs)
				rightArrow.classList.add("inactive")
			} else {
				// const debugLogs = {
				// 	trueOrFalse: false,
				// 	currentSlide,
				// 	maxSlides,
				// 	condition: "currentSlide >= maxSlides - totalSlides - 1",
				// }
				// console.table(debugLogs)
				rightArrow.classList.remove("inactive")
			}
		}

		function createIndicators() {
			const existingIndicators = slide.querySelector(".slider-indicator")
			if (existingIndicators) {
				existingIndicators.remove()
			}

			const sliderIndicator = document.createElement("div")
			sliderIndicator.classList.add("slider-indicator")

			// Berechne die Anzahl der Gruppen (Indikatoren) basierend auf der Aggregation:
			const totalIndicators = Math.ceil((maxSlides - getMaxSlidesInView()) / sliderAggregator) + 1
			console.log("totalIndicators: ", totalIndicators)

			for (let i = 0; i < totalIndicators; i++) {
				const indicatorItem = document.createElement("div")
				indicatorItem.classList.add("slider-indicator-item")
				indicatorItem.setAttribute("data-slide-index", i)

				indicatorItem.addEventListener("click", function () {
					vecuraSlideList.style.transition = ""
					let targetSlide = i * sliderAggregator
					// Clamp auf den letzten gültigen Index
					if (targetSlide >= arrayOfSlideValues.length) {
						targetSlide = arrayOfSlideValues.length - 1
					}
					currentSlide = targetSlide
					vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
					currentTransformX = arrayOfSlideValues[currentSlide]
					updateControls()
				})

				sliderIndicator.appendChild(indicatorItem)
			}

			slide.appendChild(sliderIndicator)
			sliderIndicatorItems = sliderIndicator.querySelectorAll(".slider-indicator-item")
			updateControls()
		}

        function setIndicatorActive() {
            const totalIndicators = Math.ceil((maxSlides - getMaxSlidesInView()) / sliderAggregator) + 1
            console.log("totalIndicators: ", totalIndicators)
            let currentGroup = 0

            if (totalIndicators > 1) {
                const maxSlideIndex = arrayOfSlideValues.length - 1
                const step = maxSlideIndex / (totalIndicators - 1)
                // Ordne currentSlide proportional auf die Gruppen
                currentGroup = Math.floor(currentSlide / step)
            }

            console.log("currentGroup: ", currentGroup)

            sliderIndicatorItems.forEach((item, i) => {
                if (i === currentGroup) {
                    item.classList.add("active")
                } else {
                    item.classList.remove("active")
                }
            })
        }

		// function updateSliderPositionDisplay(currentSlide, totalSlides, displayContainer) {
		// 	console.log("updateSliderPositionDisplay called")
		// 	requestAnimationFrame(() => {
		// 		const displaySpan = displayContainer.querySelector("span")
		// 		const displayWidth = displayContainer.offsetWidth
		// 		// Berechne die Breite eines einzelnen Indikators
		// 		const spanWidth = displayWidth / totalSlides
		// 		displaySpan.style.width = `${spanWidth}px`
		// 		// Verschiebe den Indikator entsprechend der aktuellen Slide-Position
		// 		displaySpan.style.transform = `translateX(${currentSlide * spanWidth}px)`
		// 		// Zeige den Indikator kurzfristig an
		// 		displayContainer.style.opacity = 1
		// 		setTimeout(() => {
		// 			displayContainer.style.opacity = 0
		// 		}, 1000)
		// 	})
		// }

        function updateSliderPositionDisplay(currentSlide, totalSlides, displayContainer) {
            console.log("updateSliderPositionDisplay called")
            requestAnimationFrame(() => {
                const displaySpan = displayContainer.querySelector("span")
                const displayWidth = displayContainer.offsetWidth

                if (totalSlides <= 1) {
                    // Falls es nur einen Slide gibt, nimm die ganze Breite
                    displaySpan.style.width = `${displayWidth}px`
                    displaySpan.style.transform = `translateX(0px)`
                } else {
                    // Berechne die Breite eines einzelnen Indikators (Segmentbreite)
                    let spanWidth = displayWidth / totalSlides
                    if (slide.getAttribute("data-hero-slider") === "true") {
                        console.log("updateSliderPositionDisplay: data-hero-slider === 'true'")
                        spanWidth = displayWidth / (totalSlides + 1)
                    }
                    // Der maximale Verschiebungswert soll so sein, dass der Indikator nicht über den Container hinaus geht:
                    const maxTranslate = displayWidth - spanWidth
                    // currentSlide reicht von 0 bis totalSlides - 1. Daraus berechnen wir den prozentualen Anteil:
                    let translate = (currentSlide / (totalSlides - 1)) * maxTranslate
                    if (slide.getAttribute("data-hero-slider") === "true") {
                        translate = (currentSlide / (totalSlides)) * maxTranslate
                    }

                    const debugLog = {
                        spanWidth,
                        maxTranslate,
                        translate,
                    }
                    console.log("debugLog: ", debugLog)

                    displaySpan.style.width = `${spanWidth}px`
                    displaySpan.style.transform = `translateX(${translate}px)`
                }

                // Den Indikator kurz anzeigen
                displayContainer.style.opacity = 1
                setTimeout(() => {
                    displayContainer.style.opacity = 0
                }, 1000)
            })
        }


		calculateSliderValues()

		if (slideIndicatorAttribute === "indicators") {
			createIndicators()
			setIndicatorActive()
		}

		let isDragging = false
		let differenceX = 0
		let differenceY = 0
		let newTransform = 0
		let startX = 0
		let startY = 0
		let whileX = 0
		let whileY = 0
		let endX = 0

		function dragStart(e) {
			if (e.type === "touchstart" || e.button === 0) {
				isDragging = true
				startX = e.type === "touchstart" ? e.touches[0].clientX : e.clientX
				startY = e.type === "touchstart" ? e.touches[0].clientY : e.clientY

				vecuraSlideWrapper.classList.add("is-dragging")
			}
		}

		// transition: all 0.33s cubic-bezier(.44,1.7,.54,.76);
		function dragging(e) {
			if (!isDragging) return
			whileX = e.type === "touchmove" ? e.touches[0].clientX : e.clientX
			whileY = e.type === "touchmove" ? e.touches[0].clientY : e.clientY
			differenceX = startX - whileX
			differenceY = startY - whileY
			newTransform = currentTransformX - differenceX

			if (Math.abs(differenceX) > Math.abs(differenceY)) {
				e.preventDefault()
			}

			// Verhindern, dass der Slider über den letzten oder ersten Slide hinausgezogen wird
			if (newTransform <= arrayOfSlideValues[arrayOfSlideValues.length - 1] + arrayOfSlideValues[1] / 2) {
				dragEnd(e)
				// vecuraSlideList.style.transition = "all 0.5s cubic-bezier(.44,1.7,.48,.83)"
				vecuraSlideList.style.transition = "all 0.5s cubic-bezier(.38,1.53,.58,.94)"
				return
			}
			if (newTransform >= arrayOfSlideValues[0] - arrayOfSlideValues[1] / 2) {
				dragEnd(e)
				// vecuraSlideList.style.transition = "all 0.5s cubic-bezier(.44,1.7,.48,.83)"
				vecuraSlideList.style.transition = "all 0.5s cubic-bezier(.38,1.53,.58,.94)"
				return
			}
			vecuraSlideList.style.transition = "none"
			vecuraSlideList.style.transform = `translate(${newTransform}px, 0px)`
		}

		function dragEnd(e) {
			if (!isDragging) return
			isDragging = false
			if (whileX === 0) return
			endX = e.type === "touchend" ? e.changedTouches[0].clientX : e.clientX
			vecuraSlideList.style.transition = ""
			/* For default and team */
			// let snapFraction = arrayOfSlideValues[1];
			// currentSlide = Math.round(newTransform / snapFraction);
			// currentSlide = Math.max(0, Math.min(currentSlide, arrayOfSlideValues.length - 1));
			// let finalTransformValue = snapFraction * currentSlide;

			/* With slideAggregator */
			let slidesPerGroup = sliderAggregator // Anzahl der Slides pro Gruppe
			let snapFraction = arrayOfSlideValues[1] // Wert für das Einrasten, entspricht einem Slide

			// Berechne die verschobene Strecke als Prozentwert der Breite eines Slide-Bereichs
			let draggedPercentage = (Math.abs(newTransform - currentTransformX) / (snapFraction * slidesPerGroup)) * -1

			// Wenn die Schwelle überschritten wird, gehe zum nächsten Abschnitt, ansonsten bleibe beim aktuellen
			if (draggedPercentage > dragThreshold) {
				// Berechne die neue Slide-Position basierend auf der Drag-Richtung
				if (newTransform < currentTransformX) {
					// Nach rechts gezogen
					currentSlide = Math.ceil(newTransform / (snapFraction * slidesPerGroup)) * slidesPerGroup
				} else {
					// Nach links gezogen
					currentSlide = Math.floor(newTransform / (snapFraction * slidesPerGroup)) * slidesPerGroup
				}
			}

			// Stelle sicher, dass currentSlide innerhalb der Grenzen bleibt
			currentSlide = Math.max(0, Math.min(currentSlide, arrayOfSlideValues.length - 1))

			let finalTransformValue = arrayOfSlideValues[currentSlide]

			// set the final transform value
			vecuraSlideList.style.transform = `translate(${finalTransformValue}px, 0px)`
			whileX = 0
			currentTransformX = finalTransformValue
			newTransform = 0

			vecuraSlideWrapper.classList.remove("is-dragging")

			updateControls()
		}

		// Exit function if there are not more slides than initialy showed
		const handleDragAndDropListeners = () => {
			if (maxSlides > getMaxSlidesInView()) {
				vecuraSlideWrapper.style = "cursor: pointer"

				vecuraSlideWrapper.addEventListener("mousedown", dragStart)
				vecuraSlideWrapper.addEventListener("touchstart", dragStart)
				document.body.addEventListener("mousemove", dragging)
				document.body.addEventListener("touchmove", dragging, {passive: false})
				document.body.addEventListener("mouseup", dragEnd)
				vecuraSlideWrapper.addEventListener("touchend", dragEnd)
			} else {
				vecuraSlideWrapper.style = "cursor: default !important"

				vecuraSlideWrapper.removeEventListener("mousedown", dragStart)
				vecuraSlideWrapper.removeEventListener("touchstart", dragStart)
				document.body.removeEventListener("mousemove", dragging)
				document.body.removeEventListener("touchmove", dragging, {passive: false})
				document.body.removeEventListener("mouseup", dragEnd)
				vecuraSlideWrapper.removeEventListener("touchend", dragEnd)
			}
		}
		handleDragAndDropListeners()

		// Funktion für das Navigieren
		function navigateSlider(direction) {
            let newSlide = currentSlide + direction * sliderAggregator
            
            const debugTable = {
                currentSlide,
                direction,
                sliderAggregator,
                newSlide,
            }
            // console.table(debugTable);
            // console.log("arrayOfSlideValues: ", arrayOfSlideValues)
            
			// Grenzen setzen
			if (newSlide < 0) {
				newSlide = 0
			} else if (newSlide > totalSlides) {
				newSlide = maxSlides - totalSlides - 1
			}

			// Setze die Transition und die neue Position
			vecuraSlideList.style.transition = ""
			currentSlide = newSlide
            // console.log("arrayOfSlideValues[currentSlide]: ", arrayOfSlideValues[currentSlide])
			vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
			currentTransformX = arrayOfSlideValues[currentSlide]

			// Controls updaten
			updateControls()
		}

		// Event Listener für die Pfeile
		leftArrow.addEventListener("click", function () {
			navigateSlider(-1) // Bewegt sich nach links (negative Richtung)
		})

		rightArrow.addEventListener("click", function () {
			navigateSlider(1) // Bewegt sich nach rechts (positive Richtung)
		})

		window.addEventListener("resize", () => {
			vecuraSlideList.style.transition = "none"
			calculateSliderValues()
			updateControls()
			handleDragAndDropListeners()
			vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
			vecuraSlideList.style.transition = ""
		})
	})
}


// initAllSliders()