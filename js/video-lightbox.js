document.addEventListener("DOMContentLoaded", function () {
    console.log('Video Lightbox Script loaded!')

	// --- Lightbox/Modal erstellen ---
	const videoModal = document.createElement("div")
	videoModal.classList.add("video-modal")
	// Optional: Style-Klassen in CSS definieren (z. B. .video-modal { display: none; ... } und .video-modal.active { display: block; })

	const videoModalCloseButton = document.createElement("div")
	videoModalCloseButton.classList.add("video-modal-close-button")
	videoModalCloseButton.innerHTML = `
    <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18 18 6M6 6l12 12"/>
    </svg>`
	videoModal.appendChild(videoModalCloseButton)

	// Füge das Modal vor ein vorhandenes Element ein oder ans Ende des Body
	const headerSpace = document.getElementById("header-space")
	if (headerSpace) {
		headerSpace.parentNode.insertBefore(videoModal, headerSpace)
	} else {
		document.body.appendChild(videoModal)
	}

	// --- Video-Container/Trigger definieren ---
	// Hier gehen wir davon aus, dass es ein Element gibt, auf das geklickt wird (z. B. ein Container oder ein Button)
	const videoContainer = document.querySelector(".video-container")
	// const playBtn = document.querySelector("button.play-video")
	const playBtn = document.querySelector(".social-wall-hero-container .featured-image")
    const altPlayBtn = document.querySelector(".content-play-button")

	// --- Video-Daten (aus WP-Meta, via wp_localize_script oder ein globales Objekt) ---
	// Beispiel: videoData wird per wp_localize_script bereitgestellt
	// videoData = { useYoutubeFrame: 1, youtubeVideoUrl: "VIDEO_ID", videoUrl: "selfhosted.mp4", videoHeadline: "Mein Video" }
	if (typeof videoData === "undefined") {
		console.error("videoData ist nicht definiert!")
		return
	}

	// --- Video erstellen ---
	let videoElement // für self-hosted Video
	let player // für YouTube
	let playerCreated = false

	// Prüfe, ob YouTube genutzt werden soll:
	let videoUrl = ""
	if (videoData.useYoutubeFrame == 1) {
		// Bei YouTube erwarten wir, dass youtubeVideoUrl die Video-ID ist
		videoUrl = videoData.youtubeVideoUrl
	} else {
		videoUrl = videoData.videoUrl
	}

	console.log("videoUrl: ", videoUrl)

	// Erstelle je nach Einstellung den Video-Inhalt:
	if (videoUrl && videoData.useYoutubeFrame == 0) {
		// Self-hosted Video: Erzeuge ein <video>-Element
		videoElement = document.createElement("video")
		videoElement.controls = true
		const sourceElement = document.createElement("source")
		sourceElement.src = videoUrl
		sourceElement.type = "video/mp4"
		videoElement.appendChild(sourceElement)
		videoModal.appendChild(videoElement)
	} else if (videoUrl && videoData.useYoutubeFrame == 1) {
		// YouTube: Erzeuge einen Container für den Player
		const playerDiv = document.createElement("div")
		playerDiv.id = "player"
		videoModal.appendChild(playerDiv)

		// Lade die YouTube IFrame API, falls noch nicht geladen
		if (typeof YT === "undefined" || typeof YT.Player === "undefined") {
			const tag = document.createElement("script")
			tag.src = "https://www.youtube.com/iframe_api"
			const firstScriptTag = document.getElementsByTagName("script")[0]
			firstScriptTag.parentNode.insertBefore(tag, firstScriptTag)
		}
		// Definiere global onYouTubeIframeAPIReady, falls nicht schon vorhanden
		window.onYouTubeIframeAPIReady = function () {
			player = new YT.Player("player", {
				height: "auto",
				width: "100%",
				videoId: videoUrl,
				playerVars: {
					playsinline: 1,
					controls: 1,
				},
			})
			playerCreated = true
		}
	} else {
		console.error("videoUrl ist nicht definiert oder ungültig.")
	}

	// --- Modal öffnen und Video abspielen ---
	function openVideoModal() {
		videoModal.classList.add("active")
		// Falls YouTube genutzt wird und der Player bereits erstellt ist, Video abspielen
		if (videoData.useYoutubeFrame == 1 && playerCreated && player && typeof player.playVideo === "function") {
			player.playVideo()
		} else if (videoElement && typeof videoElement.play === "function") {
			videoElement.play()
		}
	}

	// --- Modal schließen und Video pausieren ---
	function closeVideoModal() {
		videoModal.classList.remove("active")
		if (videoData.useYoutubeFrame == 1 && player && typeof player.pauseVideo === "function") {
			player.pauseVideo()
		} else if (videoElement && typeof videoElement.pause === "function") {
			videoElement.pause()
		}
	}

	// --- Event Listener hinzufügen ---
	// Für den Trigger (z. B. Container oder Play-Button)
	function addTouchAndClickListeners(element, callback) {
		if (!element) return
		element.addEventListener("click", callback)
		element.addEventListener("touchstart", function (event) {
			callback(event)
			event.preventDefault()
		})
	}
	addTouchAndClickListeners(videoContainer, openVideoModal)
	addTouchAndClickListeners(playBtn, openVideoModal)
	addTouchAndClickListeners(altPlayBtn, openVideoModal)

	// Modal schließen, wenn außerhalb (oder auf den Close-Button) geklickt wird
	videoModal.addEventListener("click", function (e) {
		// Schließe, wenn das Modal oder der Close-Button geklickt wird (aber nicht der Player selbst)
		if (e.target === videoModal || e.target.closest(".video-modal-close-button")) {
			closeVideoModal()
		}
	})
})
