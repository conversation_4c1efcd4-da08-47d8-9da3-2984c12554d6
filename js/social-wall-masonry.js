// Social wall mansonry.js - KORRIGIERTE VERSION
;(function ($) {
	"use strict"

	// Masonry-Instanzen speichern
	var masonryInstances = {};

	// Funktion zum Initialisieren des Masonry Layouts
	function initMasonry() {
		$(".social-wall-posts").each(function () {
			var $grid = $(this);
			var id = $(this).attr('id');

			// Masonry initialisieren nachdem alle Bilder geladen sind
			$grid.imagesLoaded(function () {
				var instance = $grid.masonry({
					itemSelector: ".social-wall-post",
					columnWidth: ".social-wall-post",
					percentPosition: false,
					gutter: 20,
				}).data('masonry');
				
				// Instanz für späteren Zugriff speichern
				if (id) {
					masonryInstances[id] = instance;
				}
			});
			
			// ENTFERNT: Der problematische layoutComplete-Handler
			// $grid.on("layoutComplete", function () {
			//     $(window).trigger("resize")
			// })
		});
	}

	// <PERSON><PERSON> der Seite initialisieren
	$(document).ready(function () {
		initMasonry();
		
		// Resize-Handler mit Verzögerung (Debounce)
		var resizeTimer;
		$(window).resize(function () {
			clearTimeout(resizeTimer);
			resizeTimer = setTimeout(function() {
				$(".social-wall-posts").masonry("layout");
			}, 250);
		});
	});
	
	// Globale Funktion für andere Scripts
	window.getSocialWallMasonryInstance = function(id) {
		return masonryInstances[id];
	};
})(jQuery);