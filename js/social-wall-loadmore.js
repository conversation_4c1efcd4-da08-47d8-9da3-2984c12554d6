document.addEventListener("DOMContentLoaded", function () {
    // Alle Load-More Buttons finden
    const buttons = document.querySelectorAll(".load-more-social");

    buttons.forEach(button => {
        const containerId = button.getAttribute("data-container-id");
        const socialContainer = document.getElementById(containerId);

        if (!socialContainer) return;

        // KEINE Masonry-Initialisierung hier, da sie bereits in social-wall-masonry.js erfolgt

        button.addEventListener("click", function () {
            let page = parseInt(button.getAttribute("data-page")) + 1;
            let maxPages = parseInt(button.getAttribute("data-max"));
            let count = parseInt(button.getAttribute("data-count"));

            if (page > maxPages) {
                button.style.display = "none";
                return;
            }

            // Taxonomies sammeln
            let taxonomies = {};
            if (socialContainer.dataset.taxonomies) {
                try {
                    taxonomies = JSON.parse(socialContainer.dataset.taxonomies);
                } catch (e) {
                    console.error("<PERSON><PERSON> beim Parsen der Taxonomies:", e);
                }
            }

            let xhr = new XMLHttpRequest();
            xhr.open("POST", social_wall_ajax_object.ajax_url, true);
            xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
            xhr.onload = function () {
                if (xhr.status >= 200 && xhr.status < 400) {
                    let response = xhr.responseText;
                    let tempDiv = document.createElement("div");
                    tempDiv.innerHTML = response;

                    let newPosts = Array.from(tempDiv.querySelectorAll(".social-wall-post"));

                    // Füge Elemente unsichtbar hinzu
                    newPosts.forEach((post) => {
                        post.style.opacity = "0";
                        post.style.transform = "translateY(20px)";
                        socialContainer.appendChild(post);
                    });

                    button.setAttribute("data-page", page);
                    if (page >= maxPages) button.style.display = "none";

                    // Bilder laden + bestehende Masonry-Instanz aktualisieren
                    imagesLoaded(socialContainer, function () {
                        // Die existierende Masonry-Instanz holen
                        let masonryInstance;
                        
                        // Versuche 1: Über gespeicherte Instanzen
                        if (window.getSocialWallMasonryInstance && socialContainer.id) {
                            masonryInstance = window.getSocialWallMasonryInstance(socialContainer.id);
                        }
                        
                        // Versuche 2: Über jQuery
                        if (!masonryInstance) {
                            // Manchmal speichert jQuery die Instanz direkt am Element
                            masonryInstance = jQuery(socialContainer).data('masonry');
                        }
                        
                        if (masonryInstance) {
                            // Methode 1: Direkt mit der Instanz
                            masonryInstance.appended(newPosts);
                            masonryInstance.layout();
                        } else {
                            // Fallback: Über jQuery (kann Fehler verursachen, wenn nicht initialisiert)
                            try {
                                jQuery(socialContainer).masonry('appended', newPosts);
                                jQuery(socialContainer).masonry('layout');
                            } catch (e) {
                                console.error("Masonry-Fehler:", e);
                                // Notfall-Fallback: Element nur anzeigen
                                newPosts.forEach(post => post.style.opacity = "1");
                            }
                        }

                        // Sanfte Einblendung nach Masonry-Update
                        setTimeout(() => {
                            newPosts.forEach((post) => {
                                post.style.transition = "opacity 0.4s ease-out, transform 0.4s ease-out";
                                post.style.opacity = "1";
                                post.style.transform = "translateY(0)";
                            });
                        }, 100);
                    });
                }
            };

            let taxonomiesParam = encodeURIComponent(JSON.stringify(taxonomies));
            xhr.send("action=load_more_social&nonce=" + social_wall_ajax_object.nonce + 
                    "&page=" + page + "&count=" + count + "&taxonomies=" + taxonomiesParam);
        });
    });
});