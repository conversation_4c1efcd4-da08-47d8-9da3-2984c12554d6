document.addEventListener("DOMContentLoaded", function () {
	// Alle Load-More Buttons finden
	const buttons = document.querySelectorAll(".load-more-news");

	buttons.forEach(button => {
		const containerId = button.getAttribute("data-container-id");
		const newsContainer = document.querySelector(`.news-container[data-container-id="${containerId}"]`);

		if (!newsContainer) return;

		let msnry = new Masonry(newsContainer, {
			itemSelector: ".news-post",
			columnWidth: ".news-post",
			percentPosition: true,
			gutter: 20,
			transitionDuration: "0.3s",
		});

		button.addEventListener("click", function () {
			let page = parseInt(button.getAttribute("data-page")) + 1;
			let maxPages = parseInt(button.getAttribute("data-max"));
			let count = parseInt(button.getAttribute("data-count"));

			if (page > maxPages) {
				button.style.display = "none";
				return;
			}

			let xhr = new XMLHttpRequest();
			xhr.open("POST", news_ajax_object.ajax_url, true);
			xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
			xhr.onload = function () {
				if (xhr.status >= 200 && xhr.status < 400) {
					let response = xhr.responseText;
					let tempDiv = document.createElement("div");
					tempDiv.innerHTML = response;

					let newPosts = Array.from(tempDiv.querySelectorAll(".news-post"));

					// Füge Elemente unsichtbar hinzu
					newPosts.forEach((post) => {
						post.style.opacity = "0";
						post.style.transform = "translateY(20px)";
						newsContainer.appendChild(post);
					});

					button.setAttribute("data-page", page);
					if (page >= maxPages) button.style.display = "none";

					// Bilder laden + Masonry aktualisieren
					imagesLoaded(newsContainer, function () {
						msnry.appended(newPosts);
						msnry.layout();

						// Sanfte Einblendung nach Masonry-Update
						setTimeout(() => {
							newPosts.forEach((post) => {
								post.style.transition = "opacity 0.4s ease-out, transform 0.4s ease-out";
								post.style.opacity = "1";
								post.style.transform = "translateY(0)";
							});
						}, 100);
					});
				}
			};

			xhr.send("action=load_more_news&nonce=" + news_ajax_object.nonce + "&page=" + page + "&count=" + count);
		});
	});
});


/*
document.addEventListener("DOMContentLoaded", function () {
	const button = document.getElementById("load-more-news")
	const newsContainer = document.getElementById("news-container")

	if (!button || !newsContainer) return

	let msnry = new Masonry(newsContainer, {
		itemSelector: ".news-post",
		columnWidth: ".news-post",
		percentPosition: true,
		gutter: 20, // ✅ Gutter-Wert erneut setzen
	})

	button.addEventListener("click", function () {
		let page = parseInt(button.getAttribute("data-page")) + 1
		let maxPages = parseInt(button.getAttribute("data-max"))
		let count = parseInt(button.getAttribute("data-count"))

		if (page > maxPages) {
			button.style.display = "none"
			return
		}

		let xhr = new XMLHttpRequest()
		xhr.open("POST", news_ajax_object.ajax_url, true)
		xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded")
		xhr.onload = function () {
			if (xhr.status >= 200 && xhr.status < 400) {
				let response = xhr.responseText
				let tempDiv = document.createElement("div")
				tempDiv.innerHTML = response

				let newPosts = tempDiv.querySelectorAll(".news-post")
                console.log(newPosts)
				newPosts.forEach((post) => newsContainer.appendChild(post))

				button.setAttribute("data-page", page)
				if (page >= maxPages) button.style.display = "none"

				// 🏗️ Masonry neu laden + Gutter fixen
				imagesLoaded(newsContainer, function () {
					msnry.appended(newPosts)
					msnry.layout()
				})
			}
		}

		xhr.send("action=load_more_news&nonce=" + news_ajax_object.nonce + "&page=" + page + "&count=" + count)
	})
})
*/
