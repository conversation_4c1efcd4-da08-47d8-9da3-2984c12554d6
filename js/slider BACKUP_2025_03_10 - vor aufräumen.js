/**
 * <PERSON><PERSON>t for slider
 */

// Definiere Funktionen außerhalb der Schleife für eine bessere Wiederverwendbarkeit und Testbarkeit
function splitValueUnit(value) {
	const regex = /^(\d+)(\D+)$/
	const match = value.match(regex)

	if (match) {
		return {
			number: parseFloat(match[1]),
			unit: match[2],
		}
	}

	throw new Error("Invalid format")
}

function initAllSliders() {

	// return 

	console.log("init all sliders")

	const sliderLinks = document.querySelectorAll("[data-link-url]")

	const timeTrashhold = 200
	const mouseMoveTrashhold = 8

	sliderLinks.forEach((link) => {
		// console.log('link: ', link);
		// console.log(link.getAttribute('data-link-url'));

		let isDragging = false
		let startTime
		let startX
		let startY

		link.addEventListener("mousedown", (e) => {
			isDragging = false
			startTime = new Date().getTime()
			startX = e.clientX
			startY = e.clientY
		})

		link.addEventListener("mousemove", (e) => {
			if (Math.abs(e.clientX - startX) > mouseMoveTrashhold || Math.abs(e.clientY - startY) > mouseMoveTrashhold) {
				isDragging = true
			}
		})

		link.addEventListener("mouseup", (e) => {
			const endTime = new Date().getTime()
			const timeDiff = endTime - startTime

			if (!isDragging && timeDiff < timeTrashhold) {
				const linkUrl = link.getAttribute("data-link-url")
				console.log("linkUrl: ", linkUrl)

				const linkTarget = link.getAttribute("data-link-target")
				if (linkTarget === "_blank") {
					window.open(linkUrl, "_blank")
				} else {
					window.location.href = linkUrl
				}
			}
		})

		link.addEventListener("touchstart", (e) => {
			isDragging = false
			startTime = new Date().getTime()
			startX = e.touches[0].clientX
			startY = e.touches[0].clientY
		})

		link.addEventListener("touchmove", (e) => {
			if (Math.abs(e.touches[0].clientX - startX) > mouseMoveTrashhold || Math.abs(e.touches[0].clientY - startY) > mouseMoveTrashhold) {
				isDragging = true
			}
		})

		link.addEventListener("touchend", (e) => {
			const endTime = new Date().getTime()
			const timeDiff = endTime - startTime

			if (!isDragging && timeDiff < timeTrashhold) {
				const linkUrl = link.getAttribute("data-link-url")
				const linkTarget = link.getAttribute("data-link-target")
				if (linkTarget === "_blank") {
					window.open(linkUrl, "_blank")
				} else {
					window.location.href = linkUrl
				}
			}
		})
	})

	const vecuraSlide = document.querySelectorAll(".vecura-slide")
	console.log("vecuraSlide: ", vecuraSlide)

	vecuraSlide.forEach((slide) => {
		console.log("slide: ", slide)

		// data attributes
		const isHeroSlider = slide.getAttribute("data-hero-slider") === "true"
		// const isCarousel = slide.getAttribute("data-carusell") === "true"
		const isCarousel = isHeroSlider
		const useArrows = slide.getAttribute("data-use-arrows") === "true"
		console.log("useArrows: ", useArrows)

		const vecuraSlideWrapper = slide.querySelector(".vecura-slide__wrapper")
		const vecuraSlideList = slide.querySelector(".vecura-slide__list")
		let vecuraSlideItem = slide.querySelectorAll(".vecura-slide__item")

		// arrows
		const arrowWrapper = slide.querySelector(".arrow-wrapper")
		const leftArrow = arrowWrapper.querySelector(".left")
		const rightArrow = arrowWrapper.querySelector(".right")

		// slider position display
		const sliderPositionDisplay = slide.querySelector(".slider-position-display")
		let maxSlides = vecuraSlideItem.length
		console.log("maxSlides: ", maxSlides)
		// const totalSlides = slide.getAttribute("data-hero-slider") === "true" ? maxSlides - 1 : maxSlides - getMaxSlidesInView() + 1

		const visibleSlides = getMaxSlidesInView() // gibt z. B. 1 bei hero_slider zurück
		const totalPositions = maxSlides - visibleSlides + 1 // einheitliche Berechnung

		let slideIndicatorAttribute = slide.getAttribute("data-slide-indicator")

		let currentSlide = 0
		let arrayOfSlideValues = []
		let currentTransformX = 0
		let maxSlidesInView = null
		let sliderIndicatorItems
		let sliderAggregator = 1 // is for snapping on dnd (1 snaps on each slide)
		let dragThreshold = 0.25

		function initCarousel(slide, vecuraSlideList) {
			console.log("%c initCarousel called", "color: red;")
			// Hole alle echten Slides
			const slides = vecuraSlideList.querySelectorAll(".vecura-slide__item")
			if (slides.length > 0) {
				const firstClone = slides[0].cloneNode(true)
				const lastClone = slides[slides.length - 1].cloneNode(true)
				vecuraSlideList.appendChild(firstClone)
				vecuraSlideList.insertBefore(lastClone, slides[0])
			}
			// Setze den Startindex so, dass er auf den ersten echten Slide zeigt
			currentSlide = 1

			// (Evtl. musst du hier auch die Berechnung der Positionswerte anpassen)
			vecuraSlideList.style.transition = "none"
			vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
			// vecuraSlideList.style.transition = ""
			currentTransformX = arrayOfSlideValues[currentSlide]
		}
		if (isCarousel) {
			initCarousel(slide, vecuraSlideList)
			vecuraSlideItem = slide.querySelectorAll(".vecura-slide__item")
			maxSlides = vecuraSlideItem.length
		}
		slide.style.opacity = 1

		function getMaxSlidesInView() {
			if (isHeroSlider) {
				return 1
			}

			const mobileMaxSlidesInView = 2 // 1
			const tabletMaxSlidesInView = 2 // 2
			const desktopMaxSlidesInView = parseInt(slide.getAttribute("data-slide-max-slides") || 4) // 3

			console.log("desktopMaxSlidesInView: ", desktopMaxSlidesInView)
			return window.innerWidth <= 699 ? mobileMaxSlidesInView : window.innerWidth <= 999 ? tabletMaxSlidesInView : desktopMaxSlidesInView
		}

		function getSliderAggregator() {
			let dataSlideAggregatorAttribute = parseInt(slide.getAttribute("data-side-aggregation")) || 1
			if (maxSlidesInView > 2) {
				sliderAggregator = dataSlideAggregatorAttribute
			}
			if (maxSlidesInView === 2 && dataSlideAggregatorAttribute > 1) {
				sliderAggregator = 2
			}
			if (maxSlidesInView === 1 && dataSlideAggregatorAttribute > 1) {
				sliderAggregator = 1
			}

			return sliderAggregator
		}

		function getdDragThreshold() {
			// dragThreshold = parseFloat(slide.getAttribute('data-drag-threshold')) || 0.25;
			if (maxSlidesInView === 1) {
				dragThreshold = 0.33
			}
			if (maxSlidesInView === 2 || maxSlidesInView === 3) {
				dragThreshold = 0.25
			}
			if (maxSlidesInView > 3) {
				dragThreshold = 0.1
			}

			return dragThreshold
		}

		/*
		function calculateSliderValues() {
			console.log("%c calculateSliderValues", "font-size: 20px;")
			maxSlidesInView = getMaxSlidesInView()
			sliderAggregator = getSliderAggregator()
			dragThreshold = getdDragThreshold()

			if (currentSlide > maxSlides - maxSlidesInView) currentSlide = maxSlides - maxSlidesInView

			let gapCount = maxSlidesInView - 1
			let slideGapAttribute = slide.getAttribute("data-slide-gap")
			console.log("slideIndicatorAttribute: ", slideIndicatorAttribute)
			let slideGap = splitValueUnit(slideGapAttribute)
			let slideFraction = 100 / maxSlidesInView
			let sliderWrapperWidth = vecuraSlideWrapper.offsetWidth
			let slideFractionInPx = (sliderWrapperWidth - gapCount * slideGap.number) / maxSlidesInView
			let minusSliderWidth = (slideGap.number * gapCount) / maxSlidesInView
			let sliderWidth = `calc(${slideFraction}% - ${minusSliderWidth}px)`

			vecuraSlideItem.forEach((slideItem) => {
				slideItem.style.minWidth = sliderWidth
				slideItem.style.marginRight = `${slideGap.number}${slideGap.unit}`
			})

			arrayOfSlideValues = []
			for (let i = 0; i <= maxSlides - maxSlidesInView; i++) {
				// let tempCurrentTransformX = -slideFractionInPx * i - slideGap.number * i
				let tempCurrentTransformX = -(slideFractionInPx + slideGap.number) * i
				arrayOfSlideValues.push(tempCurrentTransformX)
			}
		}
		*/

		function calculateSliderValues() {
			console.log("%c calculateSliderValues", "font-size: 20px;")
			maxSlidesInView = getMaxSlidesInView()
			sliderAggregator = getSliderAggregator()
			dragThreshold = getdDragThreshold()

			if (currentSlide > maxSlides - maxSlidesInView) currentSlide = maxSlides - maxSlidesInView

			let gapCount = maxSlidesInView - 1
			let slideGapAttribute = slide.getAttribute("data-slide-gap")
			console.log("slideIndicatorAttribute: ", slideIndicatorAttribute)
			let slideGap = splitValueUnit(slideGapAttribute)
			let slideFraction = 100 / maxSlidesInView
			let sliderWrapperWidth = vecuraSlideWrapper.offsetWidth

			// Präzise Berechnungen mit Math.floor oder Math.round um Rundungsfehler zu vermeiden
			let slideFractionInPx = Math.floor((sliderWrapperWidth - gapCount * slideGap.number) / maxSlidesInView)
			let minusSliderWidth = (slideGap.number * gapCount) / maxSlidesInView
			let sliderWidth = `calc(${slideFraction}% - ${minusSliderWidth}px)`

			// Direkte Messung der tatsächlichen Slide-Breite
			vecuraSlideItem.forEach((slideItem) => {
				slideItem.style.minWidth = sliderWidth
				slideItem.style.marginRight = `${slideGap.number}${slideGap.unit}`
			})

			// Wir warten kurz, bis der Browser das Layout aktualisiert hat
			requestAnimationFrame(() => {
				// Direkte Messung des ersten Slide-Elements für die exakte Breite
				const firstSlideWidth = vecuraSlideItem[0].offsetWidth
				const exactSlideOffset = firstSlideWidth + slideGap.number

				// Debug-Ausgabe
				console.log("Erste Slide-Breite:", firstSlideWidth)
				console.log("Genaue Offset-Berechnung:", exactSlideOffset)

				// Array mit den exakten Werten neu berechnen
				arrayOfSlideValues = []
				for (let i = 0; i <= maxSlides - maxSlidesInView; i++) {
					// Wir verwenden die exakt gemessene Breite und vermeiden Berechnungen
					let tempCurrentTransformX = -exactSlideOffset * i
					// Runden auf ganze Pixel um Subpixel-Rendering-Probleme zu vermeiden
					tempCurrentTransformX = Math.round(tempCurrentTransformX)
					arrayOfSlideValues.push(tempCurrentTransformX)
				}

				// Aktuell angezeigte Position aktualisieren
				vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
				currentTransformX = arrayOfSlideValues[currentSlide]

				if (slideIndicatorAttribute === "indicators") {
					setIndicatorActive()
				}
			})
		}

		function updateSlideOpacity() {
			// Nur beim Hero Slider (bei dem getMaxSlidesInView() === 1) anwenden
			if (!isHeroSlider) return

			// Zuerst alle Slides dimmen
			vecuraSlideItem.forEach((item) => {
				item.style.opacity = "0.2"
			})

			if (isCarousel) {
				// Im Carousel haben wir zwei Klone: an Index 0 (Klone des letzten echten Slides)
				// und an Index maxSlides - 1 (Klone des ersten echten Slides).
				if (currentSlide === 0) {
					// Aktiver Slide entspricht dem letzten echten Slide.
					vecuraSlideItem[0].style.opacity = "1" // Klon am Anfang
					vecuraSlideItem[maxSlides - 2].style.opacity = "1" // letzter echter Slide
				} else if (currentSlide === maxSlides - 1) {
					// Aktiver Slide entspricht dem ersten echten Slide.
					vecuraSlideItem[1].style.opacity = "1" // erster echter Slide
					vecuraSlideItem[maxSlides - 1].style.opacity = "1" // Klon am Ende
				} else {
					// In allen anderen Fällen einfach den aktuellen Slide hervorheben
					vecuraSlideItem[currentSlide].style.opacity = "1"
				}
			} else {
				// Kein Carousel: einfach den aktuellen Slide hervorheben
				vecuraSlideItem[currentSlide].style.opacity = "1"
			}
		}

		// Update arrows, slider indicators and slider position display
		const updateControls = () => {
			console.log("==> updateControls called")
			// console.log("slideIndicatorAttribute: ", slideIndicatorAttribute)
			if (slideIndicatorAttribute === "indicators") {
				console.log('slideIndicatorAttribute === "indicators"')
				setIndicatorActive()
			}
			if (slideIndicatorAttribute === "position_display") {
				sliderPositionDisplay.style.display = "block"
				// TODO: "updateSliderPositionDisplay" müsste für aggregated noch optimiert werden. Die Breite des inneren span ist so als wäre es nicht aggregated
				updateSliderPositionDisplay(currentSlide, totalPositions, sliderPositionDisplay)
			}

			// Falls hero_slider aktiv ist, erzwinge, dass die Pfeile aktiv bleiben
			if (isHeroSlider && useArrows) {
				console.log("show arrows")
				leftArrow.classList.remove("inactive")
				rightArrow.classList.remove("inactive")
				// Eventuell kannst Du hier auch spezielle Logik einbauen, falls Du trotz 1 Slide navigieren möchtest.
				updateSlideOpacity()
				return
			}

			if (!useArrows) {
				console.log("%c useArrows !== false --> return", "font-size: 18px; color: red;")
				updateSlideOpacity()
				return
			}

			if (currentSlide <= 0) {
				// const debugLogs = {
				// 	trueOrFalse: true,
				// 	currentSlide,
				// 	condition: "currentSlide <= 1",
				// }
				// console.table(debugLogs)
				leftArrow.classList.add("inactive")
			} else {
				// const debugLogs = {
				// 	trueOrFalse: false,
				// 	currentSlide,
				// 	condition: "currentSlide <= 1",
				// }
				// console.table(debugLogs)
				leftArrow.classList.remove("inactive")
			}

			if (currentSlide >= maxSlides - totalPositions - 1) {
				// const debugLogs = {
				// 	trueOrFalse: true,
				// 	currentSlide,
				// 	maxSlides,
				// 	condition: "currentSlide >= maxSlides - totalPositions - 1",
				// }
				// console.table(debugLogs)
				rightArrow.classList.add("inactive")
			} else {
				// const debugLogs = {
				// 	trueOrFalse: false,
				// 	currentSlide,
				// 	maxSlides,
				// 	condition: "currentSlide >= maxSlides - totalPositions - 1",
				// }
				// console.table(debugLogs)
				rightArrow.classList.remove("inactive")
			}

			updateSlideOpacity()
		}

		const initConrols = () => {
			console.log("initControls")

			calculateSliderValues()

			if (slideIndicatorAttribute === "indicators") {
				createIndicators()
				// setIndicatorActive wird nun in calculateSliderValues aufgerufen
				// setIndicatorActive() <- Entferne diesen Aufruf
			}

			// set position display
			if (slideIndicatorAttribute === "position_display") {
				sliderPositionDisplay.style.display = "block"
			}

			// arrows
			if (isCarousel && useArrows) {
				rightArrow.classList.remove("inactive")
				leftArrow.classList.remove("inactive")
				updateControls()
				return
			}

			if (useArrows && currentSlide > totalPositions) {
				rightArrow.classList.add("inactive")
			} else if (useArrows) {
				// rightArrow.style.opacity = 1
				rightArrow.classList.remove("inactive")
			}
			// createIndicators()
			updateControls()
		}
		initConrols()

		/* Meine eigene function 
		function createIndicators() {
			const existingIndicators = slide.querySelector(".slider-indicator")
			if (existingIndicators) {
				existingIndicators.remove()
			}

			const sliderIndicator = document.createElement("div")
			sliderIndicator.classList.add("slider-indicator")

			// Berechne die Anzahl der Gruppen (Indikatoren) basierend auf der Aggregation:
			const totalIndicators = Math.ceil((maxSlides - getMaxSlidesInView()) / sliderAggregator) + 1
			console.log("totalIndicators: ", totalIndicators)

			for (let i = 0; i < totalIndicators; i++) {
				const indicatorItem = document.createElement("div")
				indicatorItem.classList.add("slider-indicator-item")
				indicatorItem.setAttribute("data-slide-index", i)

				indicatorItem.addEventListener("click", function () {
					vecuraSlideList.style.transition = ""
					let targetSlide = i * sliderAggregator
					// Clamp auf den letzten gültigen Index
					if (targetSlide >= arrayOfSlideValues.length) {
						targetSlide = arrayOfSlideValues.length - 1
					}
					currentSlide = targetSlide
					vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
					currentTransformX = arrayOfSlideValues[currentSlide]
					updateControls()
				})

				sliderIndicator.appendChild(indicatorItem)
			}

			slide.appendChild(sliderIndicator)
			sliderIndicatorItems = sliderIndicator.querySelectorAll(".slider-indicator-item")
			updateControls()
		}
        */

		function createIndicators() {
			// Bestehende Indikatoren entfernen, falls vorhanden
			const existingIndicators = slide.querySelector(".slider-indicator")
			if (existingIndicators) {
				existingIndicators.remove()
			}

			const sliderIndicator = document.createElement("div")
			sliderIndicator.classList.add("slider-indicator")

			// Ermittel die Anzahl der realen Slides
			const realSlides = isCarousel ? maxSlides - 2 : maxSlides
			// Berechne die Gesamtanzahl der Indikatoren
			const totalIndicators = Math.ceil((realSlides - getMaxSlidesInView()) / sliderAggregator) + 1
			console.log("totalIndicators: ", totalIndicators)

			for (let i = 0; i < totalIndicators; i++) {
				const indicatorItem = document.createElement("div")
				indicatorItem.classList.add("slider-indicator-item")
				indicatorItem.setAttribute("data-slide-index", i)

				indicatorItem.addEventListener("click", function () {
					// Bei Klick den logischen Index in den DOM-Index umrechnen
					let targetSlide = isCarousel ? i * sliderAggregator + 1 : i * sliderAggregator
					// Clamp auf den letzten gültigen Index
					if (targetSlide >= arrayOfSlideValues.length) {
						targetSlide = arrayOfSlideValues.length - 1
					}
					currentSlide = targetSlide
					vecuraSlideList.style.transition = ""
					vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
					currentTransformX = arrayOfSlideValues[currentSlide]

					updateControls()
				})

				sliderIndicator.appendChild(indicatorItem)
			}

			slide.appendChild(sliderIndicator)
			sliderIndicatorItems = sliderIndicator.querySelectorAll(".slider-indicator-item")
			// updateControls()
		}

		/*
        function setIndicatorActive() {
            console.log("%c setIndicatorActive called", "font-size: 20px;")

            // Use the original approach for calculating which indicator should be active
            const totalIndicators = Math.ceil((maxSlides - getMaxSlidesInView()) / sliderAggregator) + 1
            console.log("totalIndicators: ", totalIndicators)

            let currentGroup = 0
            if (totalIndicators > 1) {
                // This is the key part that was working before:
                const maxSlideIndex = arrayOfSlideValues.length - 1
                const step = maxSlideIndex / (totalIndicators - 1)

                // For carousel, adjust the index
                const adjustedCurrentSlide = isCarousel ? currentSlide - 1 : currentSlide
                currentGroup = Math.floor(adjustedCurrentSlide / step)
            }

            console.log("currentGroup: ", currentGroup)

            sliderIndicatorItems.forEach((item, i) => {
                if (i === currentGroup) {
                    item.classList.add("active")
                } else {
                    item.classList.remove("active")
                }
            })
        }
        */

		function setIndicatorActive() {
			console.log("%c setIndicatorActive called", "font-size: 20px;")

			// Verwende die gleiche Berechnung wie in createIndicators()
			const realSlides = isCarousel ? maxSlides - 2 : maxSlides
			const totalIndicators = Math.ceil((realSlides - getMaxSlidesInView()) / sliderAggregator) + 1
			console.log("totalIndicators: ", totalIndicators)
			console.log("arrayOfSlideValues: ", arrayOfSlideValues)

			let currentGroup = 0
			if (totalIndicators > 1) {
				let maxSlideIndex = arrayOfSlideValues.length
				if (isCarousel) {
					maxSlideIndex = arrayOfSlideValues.length - 2
				}
				console.log("maxSlideIndex: ", maxSlideIndex)
				console
				const step = maxSlideIndex / totalIndicators
				console.log("step from setIndicatorActive: ", step)

				let adjustedCurrentSlide

				if (isCarousel) {
					if (currentSlide === 0) {
						// Statt hardcoded -3: Berechne den letzten Indikator basierend auf der tatsächlichen Anzahl
						currentGroup = sliderIndicatorItems.length - 1
						console.log("currentGroup: ", currentGroup)
						console.log("At first clone - setting last indicator active")

						sliderIndicatorItems.forEach((item, i) => {
							if (i === currentGroup) {
								item.classList.add("active")
							} else {
								item.classList.remove("active")
							}
						})
						return
					} else if (currentSlide === arrayOfSlideValues.length - 1) {
						currentGroup = 0
						console.log("currentGroup: ", currentGroup)
						console.log("At last clone - setting first indicator active")

						sliderIndicatorItems.forEach((item, i) => {
							if (i === currentGroup) {
								item.classList.add("active")
							} else {
								item.classList.remove("active")
							}
						})
						return
					} else {
						adjustedCurrentSlide = currentSlide - 1
					}
				} else {
					adjustedCurrentSlide = currentSlide
				}

				currentGroup = Math.floor(adjustedCurrentSlide / step)
			}

			console.log("currentGroup: ", currentGroup)

			// Stelle sicher, dass currentGroup im gültigen Bereich liegt
			currentGroup = Math.max(0, Math.min(currentGroup, sliderIndicatorItems.length - 1))
			console.log("%c Hier würde er die Indicatoren setzen", "font-size: 28px;")
			console.log("sliderIndicatorItems: ", sliderIndicatorItems)

			sliderIndicatorItems.forEach((item, i) => {
				console.log("item, i & currentGroup", item, i, currentGroup)
				if (i === currentGroup) {
					item.classList.add("active")
				} else {
					item.classList.remove("active")
				}
			})
		}

		/*
		function updateSliderPositionDisplay(currentSlide, totalPositions, displayContainer) {
			console.log("updateSliderPositionDisplay called")
			requestAnimationFrame(() => {
				const displaySpan = displayContainer.querySelector("span")
				const displayWidth = displayContainer.offsetWidth

				if (totalPositions <= 1) {
					// Falls es nur einen Slide gibt, nimm die ganze Breite
					displaySpan.style.width = `${displayWidth}px`
					displaySpan.style.transform = `translateX(0px)`
				} else {
					// Berechne die Breite eines einzelnen Indikators (Segmentbreite)
					let spanWidth = displayWidth / totalPositions
					// if (slide.getAttribute("data-hero-slider") === "true") {
					// 	console.log("updateSliderPositionDisplay: data-hero-slider === 'true'")
					// 	spanWidth = displayWidth / (totalPositions + 1)
					// }
					// Der maximale Verschiebungswert soll so sein, dass der Indikator nicht über den Container hinaus geht:
					const maxTranslate = displayWidth - spanWidth
					// currentSlide reicht von 0 bis totalPositions - 1. Daraus berechnen wir den prozentualen Anteil:
					let translate = (currentSlide / (totalPositions - 1)) * maxTranslate
					// if (slide.getAttribute("data-hero-slider") === "true") {
					// 	translate = (currentSlide / totalPositions) * maxTranslate
					// }

					const debugLog = {
						spanWidth,
						maxTranslate,
						translate,
					}
					console.log("debugLog: ", debugLog)

					displaySpan.style.width = `${spanWidth}px`
					displaySpan.style.transform = `translateX(${translate}px)`
				}

				// Den Indikator kurz anzeigen
				displayContainer.style.opacity = 1
				setTimeout(() => {
					displayContainer.style.opacity = 0
				}, 1000)
			})
		}
		*/

		// TODO: "updateSliderPositionDisplay" müsste für aggregated noch optimiert werden. Die Breite des inneren span ist so als wäre es nicht aggregated
		function updateSliderPositionDisplay(currentSlide, totalPositions, displayContainer) {
			console.log("updateSliderPositionDisplay called")
			requestAnimationFrame(() => {
				const displaySpan = displayContainer.querySelector("span")
				const displayWidth = displayContainer.offsetWidth

				// Bei Carousel: Index anpassen, um die geklonten Slides zu berücksichtigen
				let adjustedSlide = currentSlide
				if (isCarousel) {
					if (currentSlide === 0) {
						// Erster Klon: entspricht dem letzten echten Slide
						adjustedSlide = totalPositions - 1
					} else if (currentSlide === arrayOfSlideValues.length - 1) {
						// Letzter Klon: entspricht dem ersten echten Slide
						adjustedSlide = 0
					} else {
						// Normale Fälle: Reduziere den Index um 1
						adjustedSlide = currentSlide - 1
					}
				}

				if (totalPositions <= 1) {
					displaySpan.style.width = `${displayWidth}px`
					displaySpan.style.transform = `translateX(0px)`
				} else {
					let spanWidth = displayWidth / totalPositions
					const maxTranslate = displayWidth - spanWidth
					// Verwende hier den adjustierten Index
					let translate = (adjustedSlide / (totalPositions - 1)) * maxTranslate

					console.log({
						spanWidth,
						maxTranslate,
						translate,
						adjustedSlide,
						totalPositions,
					})

					displaySpan.style.width = `${spanWidth}px`
					displaySpan.style.transform = `translateX(${translate}px)`
				}

				displayContainer.style.opacity = 1
				setTimeout(() => {
					displayContainer.style.opacity = 0
				}, 1000)
			})
		}

		// calculateSliderValues()

		let isDragging = false
		let differenceX = 0
		let differenceY = 0
		let newTransform = 0
		let startX = 0
		let startY = 0
		let whileX = 0
		let whileY = 0
		let endX = 0

		function dragStart(e) {
			if (e.type === "touchstart" || e.button === 0) {
				isDragging = true
				startX = e.type === "touchstart" ? e.touches[0].clientX : e.clientX
				startY = e.type === "touchstart" ? e.touches[0].clientY : e.clientY

				vecuraSlideWrapper.classList.add("is-dragging")
			}
		}

		// transition: all 0.33s cubic-bezier(.44,1.7,.54,.76);
		// Modifiziere die dragging-Funktion
		// Globale Flag, um zu verfolgen, ob wir uns in einer Slide-Transition befinden
		let isInTransition = false

		function dragging(e) {
			if (isInTransition) {
				return
			}

			if (!isDragging) return
			whileX = e.type === "touchmove" ? e.touches[0].clientX : e.clientX
			whileY = e.type === "touchmove" ? e.touches[0].clientY : e.clientY
			differenceX = startX - whileX
			differenceY = startY - whileY
			newTransform = currentTransformX - differenceX

			if (Math.abs(differenceX) > Math.abs(differenceY)) {
				e.preventDefault()
			}

			// Für Carousel-Slider: Kein zusätzliches Begrenzen, wir lassen den Benutzer weiter ziehen
			if (!isCarousel) {
				// Für normale Slider: Verhindern, dass über den letzten oder ersten Slide hinausgezogen wird
				if (newTransform <= arrayOfSlideValues[arrayOfSlideValues.length - 1] + arrayOfSlideValues[1] / 2) {
					dragEnd(e)
					vecuraSlideList.style.transition = "all 0.5s cubic-bezier(.38,1.53,.58,.94)"
					return
				}
				if (newTransform >= arrayOfSlideValues[0] - arrayOfSlideValues[1] / 2) {
					dragEnd(e)
					vecuraSlideList.style.transition = "all 0.5s cubic-bezier(.38,1.53,.58,.94)"
					return
				}
			}

			vecuraSlideList.style.transition = "none"
			vecuraSlideList.style.transform = `translate(${newTransform}px, 0px)`
		}

		// Modifiziere die dragEnd-Funktion
		/*
		function dragEnd(e) {
			if (!isDragging) return
			isDragging = false
			if (whileX === 0) return

			endX = e.type === "touchend" ? e.changedTouches[0].clientX : e.clientX
			vecuraSlideList.style.transition = ""

			// With slideAggregator
			let slidesPerGroup = sliderAggregator // Anzahl der Slides pro Gruppe
			let snapFraction = arrayOfSlideValues[1] // Wert für das Einrasten, entspricht einem Slide

			// Berechne die verschobene Strecke als Prozentwert der Breite eines Slide-Bereichs
			let draggedPercentage = (Math.abs(newTransform - currentTransformX) / (snapFraction * slidesPerGroup)) * -1

			// Wenn die Schwelle überschritten wird, gehe zum nächsten Abschnitt, ansonsten bleibe beim aktuellen
			if (draggedPercentage > dragThreshold) {
				// Berechne die neue Slide-Position basierend auf der Drag-Richtung
				if (newTransform < currentTransformX) {
					// Nach rechts gezogen
					currentSlide = Math.ceil(newTransform / (snapFraction * slidesPerGroup)) * slidesPerGroup
					console.log("currentSlide <-: ", currentSlide)
				} else {
					// Nach links gezogen
					currentSlide = Math.floor(newTransform / (snapFraction * slidesPerGroup)) * slidesPerGroup
					console.log("currentSlide ->: ", currentSlide)
				}
			}

			// Für normale Slider: Begrenze auf verfügbare Slides
			if (!isCarousel) {
				currentSlide = Math.max(0, Math.min(currentSlide, arrayOfSlideValues.length - 1))
			}
			// Für Carousel: Erlaube alle Positionen, auch Klone

			let finalTransformValue = arrayOfSlideValues[currentSlide]

			// Setze die finale Transformation
			vecuraSlideList.style.transform = `translate(${finalTransformValue}px, 0px)`
			whileX = 0
			currentTransformX = finalTransformValue
			newTransform = 0

			vecuraSlideWrapper.classList.remove("is-dragging")

			// Für Carousel: Nach dem Loslassen prüfen, ob wir im Klonbereich sind
			if (isCarousel) {
				vecuraSlideList.addEventListener("transitionend", function transitionHandler() {
					// Prüfen, ob wir uns im Klonbereich befinden
					if (currentSlide === 0) {
						// Bei erstem Klon zum letzten echten Slide springen
						vecuraSlideList.style.transition = "none"
						currentSlide = arrayOfSlideValues.length - 2
						vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
						currentTransformX = arrayOfSlideValues[currentSlide]
						updateControls()
					} else if (currentSlide === arrayOfSlideValues.length - 1) {
						// Bei letztem Klon zum ersten echten Slide springen
						vecuraSlideList.style.transition = "none"
						currentSlide = 1
						vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
						currentTransformX = arrayOfSlideValues[currentSlide]
						updateControls()
					}

					vecuraSlideList.removeEventListener("transitionend", transitionHandler)
				})
			}

			// Controls aktualisieren
			updateControls()
		}
        */

		function dragEnd(e) {
			if (!isDragging) return
			isDragging = false
			if (whileX === 0) return

			endX = e.type === "touchend" ? e.changedTouches[0].clientX : e.clientX

			// Die Berechnung der Zielposition wie zuvor
			let slidesPerGroup = sliderAggregator
			let snapFraction = arrayOfSlideValues[1]
			let draggedPercentage = (Math.abs(newTransform - currentTransformX) / (snapFraction * slidesPerGroup)) * -1

			if (draggedPercentage > dragThreshold) {
				if (newTransform < currentTransformX) {
					currentSlide = Math.ceil(newTransform / (snapFraction * slidesPerGroup)) * slidesPerGroup
				} else {
					currentSlide = Math.floor(newTransform / (snapFraction * slidesPerGroup)) * slidesPerGroup
				}
			}

			// Für nicht-Carousel-Slider: Normale Begrenzung
			if (!isCarousel) {
				currentSlide = Math.max(0, Math.min(currentSlide, arrayOfSlideValues.length - 1))

				vecuraSlideList.style.transition = ""
				vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
				currentTransformX = arrayOfSlideValues[currentSlide]
			}
			// Für Carousel-Slider: Spezielle Behandlung
			else {
				// Wenn wir bereits in einer Transition sind, diese sofort abbrechen
				if (isInTransition) {
					vecuraSlideList.style.transition = "none"

					// Wenn wir im Klon-Bereich sind, direkt zum echten Slide springen
					if (currentSlide <= 0) {
						currentSlide = arrayOfSlideValues.length - 2
					} else if (currentSlide >= arrayOfSlideValues.length - 1) {
						currentSlide = 1
					}

					vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
					currentTransformX = arrayOfSlideValues[currentSlide]

					// Kurze Pause, dann Transition wieder aktivieren für den nächsten Slide
					setTimeout(() => {
						isInTransition = false
						vecuraSlideList.style.transition = ""
					}, 50)
				}
				// Wenn wir nicht in einer Transition sind, normale Bewegung mit Transition
				else {
					vecuraSlideList.style.transition = ""
					vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
					currentTransformX = arrayOfSlideValues[currentSlide]

					// Nur wenn wir zu einem Klon navigieren, transitionend-Handler hinzufügen
					if (currentSlide === 0 || currentSlide === arrayOfSlideValues.length - 1) {
						isInTransition = true

						const onTransitionEnd = function () {
							if (currentSlide === 0) {
								vecuraSlideList.style.transition = "none"
								currentSlide = arrayOfSlideValues.length - 2
								vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
								currentTransformX = arrayOfSlideValues[currentSlide]
							} else if (currentSlide === arrayOfSlideValues.length - 1) {
								vecuraSlideList.style.transition = "none"
								currentSlide = 1
								vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
								currentTransformX = arrayOfSlideValues[currentSlide]
							}

							// Kurze Pause, dann Transition wieder aktivieren
							setTimeout(() => {
								vecuraSlideList.style.transition = ""
								isInTransition = false
							}, 50)

							vecuraSlideList.removeEventListener("transitionend", onTransitionEnd)
						}

						vecuraSlideList.addEventListener("transitionend", onTransitionEnd)
					}
				}
			}

			whileX = 0
			newTransform = 0
			vecuraSlideWrapper.classList.remove("is-dragging")
			updateControls()
		}

		// Exit function if there are not more slides than initialy showed
		const handleDragAndDropListeners = () => {
			if (maxSlides > getMaxSlidesInView()) {
				vecuraSlideWrapper.style = "cursor: pointer"

				vecuraSlideWrapper.addEventListener("mousedown", dragStart)
				vecuraSlideWrapper.addEventListener("touchstart", dragStart)
				document.body.addEventListener("mousemove", dragging)
				document.body.addEventListener("touchmove", dragging, {passive: false})
				document.body.addEventListener("mouseup", dragEnd)
				vecuraSlideWrapper.addEventListener("touchend", dragEnd)
			} else {
				vecuraSlideWrapper.style = "cursor: default !important"

				vecuraSlideWrapper.removeEventListener("mousedown", dragStart)
				vecuraSlideWrapper.removeEventListener("touchstart", dragStart)
				document.body.removeEventListener("mousemove", dragging)
				document.body.removeEventListener("touchmove", dragging, {passive: false})
				document.body.removeEventListener("mouseup", dragEnd)
				vecuraSlideWrapper.removeEventListener("touchend", dragEnd)
			}
		}
		handleDragAndDropListeners()

		/*
		// Funktion für das Navigieren
		function navigateSlider(direction) {
			let newSlide = currentSlide + direction * sliderAggregator

			// const debugTable = {
			// 	currentSlide,
			// 	direction,
			// 	sliderAggregator,
			// 	newSlide,
			// }
			// console.table(debugTable);
			// console.log("arrayOfSlideValues: ", arrayOfSlideValues)

			// Grenzen setzen
			if (newSlide < 0) {
				newSlide = 0
			} else if (newSlide > totalPositions - 1) {
				newSlide = maxSlides - totalPositions - 1
			}

			// Setze die Transition und die neue Position
			vecuraSlideList.style.transition = ""
			currentSlide = newSlide
			// console.log("arrayOfSlideValues[currentSlide]: ", arrayOfSlideValues[currentSlide])
			vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
			currentTransformX = arrayOfSlideValues[currentSlide]

			// Controls updaten
			updateControls()
		}
        */

		// TODO: bessre Logik implementieren, die die Race Condition nicht stoppt
		// sondern dem nutzer ermöglicht so oft er will in kurzer zeit auf den Pfeil zu klicken,
		// ohne, dass die Berechnung dadurch fehlschlägt
		let isTransitioning = false // Flag, um laufende Transitionen zu verfolgen

		function navigateSlider(direction) {
			// Wenn bereits eine Transition läuft, ignoriere den Klick
			if (isTransitioning) return

			let newSlide = currentSlide + direction * sliderAggregator

			if (isCarousel) {
				// Für Carousel-Slider
				isTransitioning = true
			} else {
				// Für normalen Slider Begrenzungen setzen
				if (newSlide < 0) {
					newSlide = 0
				} else if (newSlide > totalPositions - 1) {
					newSlide = maxSlides - totalPositions - 1
				}
			}

			vecuraSlideList.style.transition = ""
			currentSlide = newSlide
			vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
			currentTransformX = arrayOfSlideValues[currentSlide]

			if (isCarousel) {
				vecuraSlideList.addEventListener("transitionend", function transitionHandler(e) {
					// Sicherstellen, dass der Übergang von "transform" stammt
					console.log("%c transitionHandler", "color: blue;")
					if (e.propertyName !== "transform") {
						console.log("%c e.propertyName !== 'transform'", e.propertyName)
						return
					}

					console.log("transition finished")
					// Befindet sich der Slider im Klonbereich, springen wir ohne Transition
					if (currentSlide === 0) {
						vecuraSlideList.style.transition = "none"
						currentSlide = arrayOfSlideValues.length - 2
						vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
						currentTransformX = arrayOfSlideValues[currentSlide]
						// updateControls()
					} else if (currentSlide === arrayOfSlideValues.length - 1) {
						vecuraSlideList.style.transition = "none"
						currentSlide = 1
						vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
						currentTransformX = arrayOfSlideValues[currentSlide]
						// updateControls()
					}

					// Transition ist abgeschlossen
					isTransitioning = false

					// Listener entfernen
					vecuraSlideList.removeEventListener("transitionend", transitionHandler)
				})
			}
			updateControls()
		}

		// Event Listener für die Pfeile
		leftArrow.addEventListener("click", function () {
			navigateSlider(-1) // Bewegt sich nach links (negative Richtung)
		})

		rightArrow.addEventListener("click", function () {
			navigateSlider(1) // Bewegt sich nach rechts (positive Richtung)
		})

		window.addEventListener("resize", () => {
			vecuraSlideList.style.transition = "none"
			calculateSliderValues()
			updateControls()
			handleDragAndDropListeners()
			vecuraSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`
			vecuraSlideList.style.transition = ""
		})
	})
}

// initAllSliders()
