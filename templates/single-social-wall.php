<?php
/**
 * Template für einzelne Social Wall Beiträge
 */

get_header(); ?>

<div class="social-wall-single-container">
    <div class="social-wall-single-content">
        <?php while (have_posts()) : the_post(); ?>
            <article id="post-<?php the_ID(); ?>" <?php post_class('social-wall-single-post'); ?>>
                <header class="entry-header">
                    <h1 class="entry-title"><?php the_title(); ?></h1>
                    
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="featured-image">
                            <?php the_post_thumbnail('large'); ?>
                        </div>
                    <?php endif; ?>
                </header>

                <div class="entry-content">
                    <?php the_content(); ?>
                </div>

                <div class="entry-meta">
                    <?php
                    // Zeige alle Taxonomien des Posts
                    $taxonomies = get_object_taxonomies('social_wall', 'objects');
                    foreach ($taxonomies as $taxonomy) {
                        $terms = get_the_terms(get_the_ID(), $taxonomy->name);
                        if ($terms && !is_wp_error($terms)) {
                            echo '<div class="taxonomy-' . esc_attr($taxonomy->name) . '">';
                            echo '<span class="taxonomy-label">' . esc_html($taxonomy->labels->singular_name) . ': </span>';
                            $term_names = wp_list_pluck($terms, 'name');
                            echo esc_html(implode(', ', $term_names));
                            echo '</div>';
                        }
                    }
                    ?>
                </div>
            </article>
        <?php endwhile; ?>
    </div>
</div>

<style>
    .social-wall-single-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }
    
    .social-wall-single-post {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .entry-header {
        margin-bottom: 2rem;
    }
    
    .entry-title {
        font-size: 2.5rem;
        margin: 1rem 0;
        color: #333;
    }
    
    .featured-image {
        margin: -2rem -2rem 2rem -2rem;
    }
    
    .featured-image img {
        width: 100%;
        height: auto;
        display: block;
    }
    
    .entry-content {
        line-height: 1.6;
        color: #444;
    }
    
    .entry-meta {
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #eee;
        font-size: 0.9rem;
        color: #666;
    }
    
    .entry-meta > div {
        margin-bottom: 0.5rem;
    }
    
    .taxonomy-label {
        font-weight: bold;
        margin-right: 0.5rem;
    }
</style>

<?php get_footer(); ?>