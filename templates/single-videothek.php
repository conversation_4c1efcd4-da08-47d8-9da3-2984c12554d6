<?php
/**
 * Template für einzelne Videothek Beiträge
 */

get_header(); ?>

<div class="videothek-single-container">
    <div class="videothek-single-content">
        <?php while (have_posts()) : the_post(); ?>
            <article id="post-<?php the_ID(); ?>" <?php post_class('videothek-single-post'); ?>>
                <header class="entry-header">
                    <h1 class="entry-title"><?php the_title(); ?></h1>
                    
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="featured-image">
                            <?php the_post_thumbnail('large'); ?>
                        </div>
                    <?php endif; ?>
                </header>

                <div class="entry-content">
                    <?php the_content(); ?>
                    
                    <?php
                    // Zeige Video-spezifische Meta-Daten
                    $video_headline = get_post_meta(get_the_ID(), 'video_headline', true);
                    $use_youtube_frame = get_post_meta(get_the_ID(), 'use_youtube_frame', true);
                    $youtube_video_url = get_post_meta(get_the_ID(), 'youtube_video_url', true);
                    $selected_video_id = get_post_meta(get_the_ID(), 'selected_video_id', true);
                    
                    if ($video_headline) {
                        echo '<h3 class="video-headline">' . esc_html($video_headline) . '</h3>';
                    }
                    
                    // Zeige Video basierend auf Typ
                    if ($use_youtube_frame && $youtube_video_url) {
                        // YouTube Video
                        echo '<div class="video-container youtube-video">';
                        echo '<iframe width="100%" height="400" src="' . esc_url($youtube_video_url) . '" frameborder="0" allowfullscreen></iframe>';
                        echo '</div>';
                    } elseif ($selected_video_id) {
                        // Self-hosted Video
                        $video_url = wp_get_attachment_url($selected_video_id);
                        if ($video_url) {
                            echo '<div class="video-container self-hosted-video">';
                            echo '<video width="100%" controls>';
                            echo '<source src="' . esc_url($video_url) . '" type="video/mp4">';
                            echo __('Ihr Browser unterstützt das Video-Tag nicht.', 'textdomain');
                            echo '</video>';
                            echo '</div>';
                        }
                    }
                    ?>
                </div>

                <div class="entry-meta">
                    <?php
                    // Zeige alle Taxonomien des Posts
                    $taxonomies = get_object_taxonomies('videothek', 'objects');
                    foreach ($taxonomies as $taxonomy) {
                        $terms = get_the_terms(get_the_ID(), $taxonomy->name);
                        if ($terms && !is_wp_error($terms)) {
                            echo '<div class="taxonomy-' . esc_attr($taxonomy->name) . '">';
                            echo '<span class="taxonomy-label">' . esc_html($taxonomy->labels->singular_name) . ': </span>';
                            $term_names = wp_list_pluck($terms, 'name');
                            echo esc_html(implode(', ', $term_names));
                            echo '</div>';
                        }
                    }
                    ?>
                </div>
            </article>
        <?php endwhile; ?>
    </div>
</div>

<style>
    .videothek-single-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }
    
    .videothek-single-post {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .entry-header {
        margin-bottom: 2rem;
    }
    
    .entry-title {
        font-size: 2.5rem;
        margin: 1rem 0;
        color: #333;
    }
    
    .featured-image {
        margin: -2rem -2rem 2rem -2rem;
    }
    
    .featured-image img {
        width: 100%;
        height: auto;
        display: block;
    }
    
    .entry-content {
        line-height: 1.6;
        color: #444;
    }
    
    .video-container {
        margin: 2rem 0;
        border-radius: 8px;
        overflow: hidden;
    }
    
    .video-container iframe,
    .video-container video {
        width: 100%;
        height: auto;
        min-height: 400px;
    }
    
    .video-headline {
        font-size: 1.5rem;
        margin: 1.5rem 0 1rem 0;
        color: #333;
    }
    
    .entry-meta {
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #eee;
        font-size: 0.9rem;
        color: #666;
    }
    
    .entry-meta > div {
        margin-bottom: 0.5rem;
    }
    
    .taxonomy-label {
        font-weight: bold;
        margin-right: 0.5rem;
    }
    
    @media (max-width: 768px) {
        .videothek-single-container {
            padding: 1rem;
        }
        
        .entry-title {
            font-size: 2rem;
        }
        
        .video-container iframe,
        .video-container video {
            min-height: 250px;
        }
    }
</style>

<?php get_footer(); ?>
