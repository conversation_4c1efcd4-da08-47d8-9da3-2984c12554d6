<?php
/**
 * Template für Videothek Archive
 */

get_header(); ?>

<div class="videothek-archive-container">
    <header class="archive-header">
        <h1 class="archive-title"><?php post_type_archive_title(); ?></h1>
        <?php if (get_the_archive_description()) : ?>
            <div class="archive-description"><?php the_archive_description(); ?></div>
        <?php endif; ?>
    </header>

    <div class="videothek-archive-content">
        <?php if (have_posts()) : ?>
            <div class="videothek-grid">
                <?php while (have_posts()) : the_post(); ?>
                    <article id="post-<?php the_ID(); ?>" <?php post_class('videothek-grid-item'); ?>>
                        <a href="<?php the_permalink(); ?>" class="videothek-item-link">
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="videothek-thumbnail">
                                    <?php the_post_thumbnail('medium'); ?>
                                    <div class="play-overlay">
                                        <span class="play-icon">▶</span>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="videothek-content">
                                <h2 class="videothek-title"><?php the_title(); ?></h2>
                                
                                <?php if (has_excerpt()) : ?>
                                    <div class="videothek-excerpt">
                                        <?php the_excerpt(); ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="videothek-meta">
                                    <?php
                                    // Zeige Taxonomien
                                    $taxonomies = get_object_taxonomies('videothek');
                                    foreach ($taxonomies as $taxonomy) {
                                        $terms = get_the_terms(get_the_ID(), $taxonomy);
                                        if ($terms && !is_wp_error($terms)) {
                                            echo '<div class="videothek-terms">';
                                            foreach ($terms as $term) {
                                                echo '<span class="term-tag">' . esc_html($term->name) . '</span>';
                                            }
                                            echo '</div>';
                                            break; // Nur erste Taxonomie anzeigen
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                        </a>
                    </article>
                <?php endwhile; ?>
            </div>

            <?php
            // Pagination
            the_posts_pagination(array(
                'mid_size' => 2,
                'prev_text' => __('← Vorherige', 'textdomain'),
                'next_text' => __('Nächste →', 'textdomain'),
            ));
            ?>

        <?php else : ?>
            <div class="no-videos-found">
                <h2><?php _e('Keine Videos gefunden', 'textdomain'); ?></h2>
                <p><?php _e('Es wurden noch keine Videos in der Videothek veröffentlicht.', 'textdomain'); ?></p>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
    .videothek-archive-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }
    
    .archive-header {
        text-align: center;
        margin-bottom: 3rem;
    }
    
    .archive-title {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: #333;
    }
    
    .archive-description {
        font-size: 1.1rem;
        color: #666;
        max-width: 600px;
        margin: 0 auto;
    }
    
    .videothek-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .videothek-grid-item {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .videothek-grid-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.2);
    }
    
    .videothek-item-link {
        text-decoration: none;
        color: inherit;
        display: block;
    }
    
    .videothek-thumbnail {
        position: relative;
        overflow: hidden;
    }
    
    .videothek-thumbnail img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .videothek-grid-item:hover .videothek-thumbnail img {
        transform: scale(1.05);
    }
    
    .play-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .videothek-grid-item:hover .play-overlay {
        opacity: 1;
    }
    
    .play-icon {
        font-size: 3rem;
        color: white;
        text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    }
    
    .videothek-content {
        padding: 1.5rem;
    }
    
    .videothek-title {
        font-size: 1.3rem;
        margin: 0 0 1rem 0;
        color: #333;
        line-height: 1.3;
    }
    
    .videothek-excerpt {
        color: #666;
        font-size: 0.9rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }
    
    .videothek-meta {
        margin-top: auto;
    }
    
    .videothek-terms {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .term-tag {
        background: #f0f0f0;
        color: #666;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .no-videos-found {
        text-align: center;
        padding: 4rem 2rem;
        color: #666;
    }
    
    .no-videos-found h2 {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: #333;
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .videothek-archive-container {
            padding: 1rem;
        }
        
        .archive-title {
            font-size: 2rem;
        }
        
        .videothek-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        
        .videothek-content {
            padding: 1rem;
        }
    }
    
    @media (max-width: 480px) {
        .videothek-grid {
            gap: 1rem;
        }
        
        .play-icon {
            font-size: 2rem;
        }
    }
</style>

<?php get_footer(); ?>
