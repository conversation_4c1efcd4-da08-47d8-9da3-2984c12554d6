<?
/** 
 * CPT Social Wall 
 * 
 * requires: masonry library, images loaded library
 * 
 */

// require_once plugin_dir_path(__FILE__) . '../includes/template-parts/social-wall-template-parts.php';
require_once MY_PLUGIN_ROOT . 'includes/template-parts/social-wall-template-parts.php';

// Registrierung des Shortcodes
add_shortcode('social_wall', 'render_social_wall_post');



/* Debugging */ 
// $pfad = MY_PLUGIN_ROOT . 'includes/template-parts/social-wall-template-parts.php';
// echo '<pre>' . esc_html($pfad) . '</pre>';
