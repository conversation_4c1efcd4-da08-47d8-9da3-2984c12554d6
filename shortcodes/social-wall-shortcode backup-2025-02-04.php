<?
/** 
 * CPT Social Wall 
 * 
 * requires: masonry library, images loaded library
 * 
 */
// Zuerst fügen wir die Masonry Library hinzu
function enqueue_masonry_scripts() {
    wp_enqueue_script('masonry');
    wp_enqueue_script('imagesloaded');
    wp_enqueue_script('social-wall-masonry', plugins_url('../js/social-wall-masonry.js', __FILE__), array('jquery', 'masonry', 'imagesloaded'), '1.0', true);
}
add_action('wp_enqueue_scripts', 'enqueue_masonry_scripts');

// Komplexer Shortcode für Social Wall mit Unterstützung für mehrere benutzerdefinierte Taxonomien
function complex_social_wall_shortcode($atts) {
    // Hole die gespeicherte Textfarbe aus den Einstellungen
    /* global styles */ 
    $text_color = get_option('vecura_mediathek_text_color', '#000000'); // Fallback auf Schwarz

    /* social wall specific styles */ 
    $category_style = get_option('vecura_mediathek_social_wall_category_style', 'default');
    $hide_categories = get_option('vecura_mediathek_social_wall_hide_categories', false);
    $hide_date = get_option('vecura_mediathek_social_wall_hide_date', false);
    $cta_text = get_option('vecura_mediathek_social_wall_cta_text', false);

    // CSS Klassen basierend auf dem gewählten Style
    $category_class = 'category-style-' . $category_style;

    // Standardwerte für die Attribute, einschließlich dynamischer Taxonomien
    $atts = shortcode_atts(array(
        'headline' => 'Default Headline',
        'tagline' => 'Default Tagline',
        'content' => 'Default Content',
        'id' => '',
    ), $atts, 'social_wall');

    $output .= '<pre>args' . print_r($args, true) . '</pre>';
    $output .= '<pre>attrs' . print_r($atts, true) . '</pre>';

    // Eindeutige ID für das Masonry-Grid
    $grid_id = 'masonry-grid-' . uniqid();

    $output = '<div class="social-wall-wrapper"' . 
        (!empty($atts['id']) ? ' id="' . esc_attr($atts['id']) . '"' : '') . 
        '>';

    if (!empty($atts['headline'])) {
        $output .= '<h2 class="social-wall-headline">' . esc_html($atts['headline']) . '</h2>';
    }

    if (!empty($atts['tagline'])) {
        $output .= '<h3 class="social-wall-tagline">' . esc_html($atts['tagline']) . '</h3>';
    }

    if (!empty($atts['content'])) {
        $output .= '<div class="social-wall-content">' . wp_kses_post($atts['content']) . '</div>';
    }

    // Query bleibt gleich
    $args = array(
        'post_type' => 'social_wall',
        'posts_per_page' => 10
    );

    // Taxonomie-Filter nur hinzufügen, wenn spezifische Taxonomien ausgewählt wurden
    $tax_query = array();
    foreach ($atts as $key => $value) {
        if (!in_array($key, array('headline', 'tagline', 'content', 'id')) && !empty($value) && $value !== 'all') {
            $tax_query[] = array(
                'taxonomy' => $key,
                'field' => 'slug',
                'terms' => explode(',', $value)
            );
        }
    }

    if (!empty($tax_query)) {
        $args['tax_query'] = array_merge(array('relation' => 'AND'), $tax_query);
    }

    $query = new WP_Query($args);

    if ($query->have_posts()) {
        $output .= '<div class="social-wall-posts">';
        while ($query->have_posts()) {
            $query->the_post();
            
            $output .= '<article class="social-wall-post">';

            /*
             * Taxonomien 
             */
            if (!$hide_categories) {
                $output .= '<div class="post-meta ' . esc_attr($category_class) . '">';
                
                $output .= '<div class="post-taxonomies">';

                    // Holen Sie die benutzerdefinierten Taxonomien für social_wall
                    $custom_taxonomies = get_option('custom_taxonomies_social_wall', array());
            
                    if (!empty($custom_taxonomies)) {
                        foreach ($custom_taxonomies as $taxonomy) {
                            $terms = get_the_terms(get_the_ID(), $taxonomy);
                            
                            if ($terms && !is_wp_error($terms)) {
                                $output .= '<div class="taxonomy-group">';

                                // Taxonomy name for debugging
                                // $output .= '<span class="taxonomy-name">' . esc_html($taxonomy) . ':</span>';

                                foreach ($terms as $term) {
                                    $svg_code = get_term_meta($term->term_id, 'term_svg', true);

                                    $output .= '<div class="single-taxonomy-term">';

                                        // SVG als zusätzliche Information ausgeben, falls vorhanden
                                        $svg_code = get_term_meta($term->term_id, 'term_svg', true);
                                        if (!empty($svg_code)) {
                                            $output .= '<span class="taxonomy-icon" title="' . esc_attr($term->name) . '">';
                                            $output .= $svg_code;
                                            $output .= '</span>';
                                        }

                                        // output category term name
                                        $output .= '<span class="taxonomy-term">' . esc_html($term->name) . '</span>';

                                    $output .= '</div>';
                                }
                                $output .= '</div>';
                            }
                        }
                    }
                    $output .= '</div>'; // Ende post-taxonomies

                    // Datum hinzufügen
                    $output .= '<div class="post-date">' . get_the_date() . '</div>';

                $output .= '</div>'; // Ende post-meta
            }
            
            // Beitragsbild
            $output .= '<div class="post-thumbnail">';
            if (has_post_thumbnail()) {
                $output .= '<a href="' . get_permalink() . '">';
                $output .= get_the_post_thumbnail(get_the_ID(), 'large');
                $output .= '</a>';
            } else {
                $output .= '<div class="no-thumbnail">';
                $output .= __('Kein Beitragsbild vorhanden', 'textdomain');
                $output .= '</div>';
            }
            $output .= '</div>';
            
            $output .= '<div class="post-content-wrapper">';
            $output .= '<h4 class="post-title">';
            // $output .= '<a href="' . get_permalink() . '">';
            $output .= get_the_title();
            // $output .= '</a>';
            $output .= '</h4>';
            
            $output .= '<div class="post-content">';
            $output .= get_the_excerpt();

            $output .= '<div class="post-cta">';
            if($cta_text) {
                $output .= '<a href="' . get_permalink() . '" class="read-more">';
                $output .= $cta_text;
                $output .= '</a>';
            } else {
                $output .= '<a href="' . get_permalink() . '" class="read-more">' . 
                          __('Weiterlesen', 'textdomain') . 
                          '</a>';
            } 
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</div>'; // Ende post-content-wrapper
            
            $output .= '</article>';
        }
        $output .= '</div>';
        wp_reset_postdata();
    }

    $output .= '</div>';

        // Styles für die Social Wall mit Bild
    $output .= '<style>
        .social-wall-wrapper {
            /* max-width: 1200px; */
            margin: 0 auto;
            color: ' . esc_attr($text_color) . ';
        }
        .social-wall-posts {
            margin: 36px 0px;
        }
        .social-wall-post {
            width: calc(33.333% - (36px * 2 / 3)); /* Breite minus Gutter */
            /* width: 386.9px; */
            margin: 0 0px 36px 0px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 0px rgba(0,0,0,0.0);
            overflow: hidden;

            transition: box-shadow 300ms ease-in-out;
        }
        .social-wall-post:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .post-thumbnail {
            position: relative;
            padding-top: 56.25%; /* 16:9 Aspect Ratio */
            overflow: hidden;
        }
        .post-thumbnail img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 300ms ease;
        }
        .social-wall-post:hover .post-thumbnail img {
            transform: scale(1.05);
        }
        .post-content-wrapper {
            padding: 1rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .post-title {
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }
        .post-title a {
            color: #333;
            text-decoration: none;
        }
        .post-title a:hover {
            color: #0066cc;
        }

        .post-meta {
            padding: 0.5rem 1rem;
            font-size: 0.9em;
            display: flex;
            justify-content: space-between;
            /* align-items: center; */
        }

        /******************************/
        /* Different post meta styles */
        /******************************/
        /* Default */ 
        /*
        .post-meta.category-style-default .taxonomy-group:first-child .single-taxonomy-term:first-child {
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        */
        .post-meta.category-style-default .taxonomy-group .single-taxonomy-term {
            padding: 0;
            margin-bottom: 0.5rem;
            display: flex;
            gap: 0.5rem;
        }
        .single-taxonomy-term span.taxonomy-icon svg {
            width: 24px;
            height: 24px;
        }

        /* icons-only */
        .post-meta.category-style-icons-only .taxonomy-term {
            display: none;
        }
        .post-meta.category-style-icons-only .taxonomy-group {
            display: flex;
        }
        /*
        .post-meta.category-style-icons-only .taxonomy-group:first-child .single-taxonomy-term:first-child {
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        */
        .post-meta.category-style-icons-only .taxonomy-group .single-taxonomy-term {
            padding: 0;
            display: flex;
            gap: 0.5rem;
            margin-right: 0.5rem;
        }
        .post-meta.category-style-icons-only .single-taxonomy-term:not(:has(.taxonomy-icon)) {
            display: none !important;
        }


        /* End post meta styles */ 

        .post-content {
            flex-grow: 1;
            line-height: 1.6;
        }
        .read-more {
            display: inline-block;
            margin-top: 15px;
            padding: 8px 16px;
            background-color: #0066cc;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 300ms ease;
        }
        .read-more:hover {
            background-color: #0052a3;
        }
        .no-thumbnail {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        @media (max-width: 992px) {
            .social-wall-posts {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        @media (max-width: 576px) {
            .social-wall-posts {
                grid-template-columns: 1fr;
            }
        }
    </style>';

    return $output;
}

// Registrierung des Shortcodes
add_shortcode('social_wall', 'complex_social_wall_shortcode');
