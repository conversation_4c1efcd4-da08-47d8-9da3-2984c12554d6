<?php
/**
 * Plugin Name: Flexible Mediathek
 * Description: Erstellt drei benutzerdefinierte Post-Typen: Social Wall, Videothek und News und fügt eine Einstellungsseite hinzu.
 * Version: 1.1
 * Author: <PERSON>
 */

// Verhindert direkten Zugriff auf die Datei
if (!defined('ABSPATH')) {
    exit;
}

// In deiner Haupt-Plugin-Datei:
if ( ! defined( 'MY_PLUGIN_ROOT' ) ) {
    define( 'MY_PLUGIN_ROOT', plugin_dir_path( __FILE__ ) );
}

if ( ! defined( 'VECURA_PLUGIN_URL' ) ) {
    define( 'VECURA_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
}

// Load all CPTs
require_once MY_PLUGIN_ROOT . 'includes/custom-post-types/index.php';
require_once MY_PLUGIN_ROOT . 'includes/taxonomies/index.php';

/**
 * Plugin Activation Hook - Flush Rewrite Rules
 */
function vecura_mediathek_activate() {
    // Lade CPTs
    require_once MY_PLUGIN_ROOT . 'includes/custom-post-types/index.php';

    // Flush rewrite rules nach CPT-Registrierung
    flush_rewrite_rules();
}
register_activation_hook(__FILE__, 'vecura_mediathek_activate');

/**
 * Plugin Deactivation Hook - Flush Rewrite Rules
 */
function vecura_mediathek_deactivate() {
    flush_rewrite_rules();
}
register_deactivation_hook(__FILE__, 'vecura_mediathek_deactivate');

/**
 * Temporäre Funktion zum Aktualisieren der Rewrite Rules
 * Diese Funktion wird nur einmal ausgeführt und dann entfernt
 */
function vecura_mediathek_flush_rewrite_rules_once() {
    $flushed = get_option('vecura_mediathek_rewrite_flushed', false);
    if (!$flushed) {
        flush_rewrite_rules();
        update_option('vecura_mediathek_rewrite_flushed', true);
    }
}
add_action('init', 'vecura_mediathek_flush_rewrite_rules_once', 999);



/**
 * 
 * Admin Seetings Page 
 * 
 */
// Lokalisierungsdaten für JavaScript hinzufügen
function vecura_add_admin_scripts($hook) {
    if('toplevel_page_x-mediathek-settings' !== $hook) {
        return;
    }

    wp_localize_script('vecura-mediathek-admin', 'vecuraData', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('vecura_mediathek_nonce'),
        // Bestehende Einstellungen laden
        'settings' => array(
            'hideCategories' => get_option('vecura_mediathek_hide_categories', false),
            // weitere Einstellungen...
        )
    ));
}
add_action('admin_enqueue_scripts', 'vecura_add_admin_scripts');



// Callback-Funktion für die Hintergrundfarbe
function vecura_mediathek_background_color_callback() {
    $color = get_option('vecura_mediathek_background_color', '#ffffff');
    echo '<input type="text" name="vecura_mediathek_background_color" value="' . esc_attr($color) . '" class="my-color-field" data-default-color="#ffffff" />';
}

// Callback-Funktion für die Textfarbe
function vecura_mediathek_text_color_callback() {
    $color = get_option('vecura_mediathek_text_color', '#000000');
    echo '<input type="text" name="vecura_mediathek_text_color" value="' . esc_attr($color) . '" class="my-color-field" data-default-color="#000000" />';
}

// Füge Color Picker zu den Einstellungen hinzu
function vecura_mediathek_color_picker_scripts($hook_suffix) {
    wp_enqueue_style('wp-color-picker');
    // wp_enqueue_script('x-mediathek-color-picker', plugins_url('x-mediator-color-picker.js', __FILE__), array('wp-color-picker'), false, true);
}
add_action('admin_enqueue_scripts', 'vecura_mediathek_color_picker_scripts');




// Shortcode-Generator oberhalb der Beitragsliste für benutzerdefinierte Post-Typen
/* Old and working but not complete 
function add_dynamic_shortcode_generator_above_post_list() {
    global $post_type;

    // Hole die gespeicherte Einstellung für die News-Quelle aus der Datenbank
    $news_source = get_option('vecura_mediathek_news_source', 'cpt');

    // Bestimme, ob der Shortcode-Generator angezeigt werden soll
    if ($post_type === 'social_wall' || $post_type === 'videothek' || $post_type === 'news' || ($post_type === 'post' && $news_source === 'posts')) {
        // Falls "posts" als News-Quelle gewählt wurde, den CPT "news" als Alias nutzen
        $shortcode_post_type = ($post_type === 'news' || $post_type === 'post') ? 'news' : $post_type;
    } else {
        return; // Falls die Bedingung nicht zutrifft, abbrechen
    }

    echo '<div class="shortcode-area">';
    echo '<h2>' . __('Generiere deinen Shortcode für diesen CPT', 'textdomain') . '</h2>';
    echo '<form id="shortcode-generator-form">';

    // Dynamisch alle benutzerdefinierten Taxonomien des aktuellen Post-Typs abrufen
    $taxonomies = get_object_taxonomies($post_type, 'objects');

    foreach ($taxonomies as $taxonomy) {
        $terms = get_terms(array(
            'taxonomy' => $taxonomy->name,
            'hide_empty' => false,
        ));

        if (!empty($terms) && !is_wp_error($terms)) {
            echo '<label>' . esc_html($taxonomy->label) . ':</label>';
            echo '<select name="taxonomy_' . esc_attr($taxonomy->name) . '">';
            echo '<option value="all">' . __('Alle', 'textdomain') . '</option>';

            foreach ($terms as $term) {
                echo '<option value="' . esc_attr($term->slug) . '">' . esc_html($term->name) . '</option>';
            }
            echo '</select><br>';
        } else {
            echo '<p>' . __('Keine Kategorien gefunden', 'textdomain') . '</p>';
        }
    }

    // Weitere Felder für den Shortcode
    echo '<label for="shortcode-headline">' . __('Headline', 'textdomain') . ':</label>';
    echo '<input type="text" id="shortcode-headline" name="headline" placeholder="z.B. Meine Headline"><br>';

    echo '<label for="shortcode-tagline">' . __('Tagline', 'textdomain') . ':</label>';
    echo '<input type="text" id="shortcode-tagline" name="tagline" placeholder="z.B. Meine Tagline"><br>';

    echo '<label for="shortcode-content">' . __('Content', 'textdomain') . ':</label>';
    echo '<textarea id="shortcode-content" name="content" placeholder="Benutzerdefinierter Inhalt"></textarea><br>';

    echo '<label for="shortcode-count">' . __('Anzahl der ersten News', 'textdomain') . ':</label>';
    echo '<input type="number" id="shortcode-count" name="count" min="1" max="50" value="6"><br>';

    echo '<label for="shortcode-id">' . __('ID', 'textdomain') . ':</label>';
    echo '<input type="text" id="shortcode-id" name="id" placeholder="z.B. my-custom-id"><br>';

    // Zusätzliche Option nur für Videos (z. B. CPT "videothek")
    if ( $shortcode_post_type === 'videothek' ) {
        echo '<label for="shortcode-slider-inbdicator">' . __('Slider Indicator', 'textdomain') . ':</label>';
        echo '<select id="shortcode-slider-inbdicator" name="slider_style">';
        echo '<option value="indicators">' . __('Slider Indicators', 'textdomain') . '</option>';
        echo '<option value="position_display">' . __('Slider Position Display', 'textdomain') . '</option>';
        echo '</select><br>';

        // Checkbox für "Image only"
        echo '<label for="shortcode-image-only">' . __('Image only', 'textdomain') . ':</label>';
        echo '<input type="checkbox" id="shortcode-image-only" name="image_only" checked><br>';
    }

    echo '<button type="button" id="generate-shortcode-button">' . __('Shortcode generieren', 'textdomain') . '</button>';
    echo '</form>';

    // Platz für den generierten Shortcode
    echo '<h3>' . __('Dein generierter Shortcode:', 'textdomain') . '</h3>';
    echo '<textarea id="generated-shortcode" readonly style="width: 100%; padding: 10px;" rows="3"></textarea>';
    echo '</div>';

    // JavaScript zur dynamischen Shortcode-Generierung
    echo '<script type="text/javascript">
    document.getElementById("generate-shortcode-button").addEventListener("click", function() {
        var shortcode = "[' . $shortcode_post_type . '";
        var headline = document.getElementById("shortcode-headline").value;
        var tagline = document.getElementById("shortcode-tagline").value;
        var content = document.getElementById("shortcode-content").value;
        var count = document.getElementById("shortcode-count").value;
        var id = document.getElementById("shortcode-id").value;

        // Dynamisch erstellte benutzerdefinierte Taxonomien verarbeiten
        var taxonomies = document.querySelectorAll("select[name^=\'taxonomy_\']");
        taxonomies.forEach(function(select) {
            var selected = select.value;
            if (selected && selected !== "all") {
                var taxonomyName = select.name.replace("taxonomy_", "");
                shortcode += " " + taxonomyName + "=\\"" + selected + "\\"";
            }
        });

        if (headline) {
            shortcode += " headline=\\"" + headline + "\\"";
        }
        if (tagline) {
            shortcode += " tagline=\\"" + tagline + "\\"";
        }
        if (content) {
            shortcode += " content=\\"" + content + "\\"";
        }
        if (count) {
            shortcode += " count=\\"" + count + "\\"";
        }
        if (id) {
            shortcode += " id=\\"" + id + "\\"";
        }

        // Zusätzliche Option für Videos: Slider Style
        var sliderIndicatorElem = document.getElementById("shortcode-slider-inbdicator");
        if (sliderIndicatorElem) {
            var sliderIndicator = sliderIndicatorElem.value;
            shortcode += " slider_indicator=\\"" + sliderIndicator + "\\"";
        }

        // "Image only" Checkbox auswerten
        var imageOnlyElem = document.getElementById("shortcode-image-only");
        if (imageOnlyElem) {
            var imageOnly = imageOnlyElem.checked ? "true" : "false";
            shortcode += " image_only=\\"" + imageOnly + "\\"";
        }

        shortcode += "]";
        document.getElementById("generated-shortcode").value = shortcode;
    });
    </script>';

    echo '<style>
    .shortcode-area {
        margin-right: 20px;
        padding: 0 1rem 1rem 1rem;
        border: 1px solid #c3c4c7;
        margin-top: 2rem;
        background: #ffffff;
    }
    form#shortcode-generator-form {
        display: flex;
        flex-direction: column;
    }
    </style>';
}
add_action("all_admin_notices", "add_dynamic_shortcode_generator_above_post_list");
*/ 


// Shortcode-Generator oberhalb der Beitragsliste für benutzerdefinierte Post-Typen
function add_dynamic_shortcode_generator_above_post_list() {
    global $post_type;

    // Hole die gespeicherte Einstellung für die News-Quelle aus der Datenbank
    $news_source = get_option('vecura_mediathek_news_source', 'cpt');

    // Bestimme, ob der Shortcode-Generator angezeigt werden soll
    if ($post_type === 'social_wall' || $post_type === 'videothek' || $post_type === 'news' || ($post_type === 'post' && $news_source === 'posts')) {
        // Falls "posts" als News-Quelle gewählt wurde, den CPT "news" als Alias nutzen
        $shortcode_post_type = ($post_type === 'news' || $post_type === 'post') ? 'news' : $post_type;
    } else {
        return; // Falls die Bedingung nicht zutrifft, abbrechen
    }

    echo '<div class="shortcode-area">';
    echo '<h2>' . __('Generiere deinen Shortcode für diesen CPT', 'textdomain') . '</h2>';
    echo '<form id="shortcode-generator-form">';

    // Dynamisch alle benutzerdefinierten Taxonomien des aktuellen Post-Typs abrufen
    $taxonomies = get_object_taxonomies($post_type, 'objects');

    foreach ($taxonomies as $taxonomy) {
        $terms = get_terms(array(
            'taxonomy' => $taxonomy->name,
            'hide_empty' => false,
        ));

        if (!empty($terms) && !is_wp_error($terms)) {
            echo '<label>' . esc_html($taxonomy->label) . ':</label>';
            echo '<select name="taxonomy_' . esc_attr($taxonomy->name) . '">';
            echo '<option value="all">' . __('Alle', 'textdomain') . '</option>';

            foreach ($terms as $term) {
                echo '<option value="' . esc_attr($term->slug) . '">' . esc_html($term->name) . '</option>';
            }
            echo '</select><br>';
        } else {
            echo '<p>' . __('Keine Kategorien gefunden', 'textdomain') . '</p>';
        }
    }

    // Weitere Felder für den Shortcode
    echo '<label for="shortcode-headline">' . __('Headline', 'textdomain') . ':</label>';
    echo '<input type="text" id="shortcode-headline" name="headline" placeholder="z.B. Meine Headline"><br>';

    echo '<label for="shortcode-tagline">' . __('Tagline', 'textdomain') . ':</label>';
    echo '<input type="text" id="shortcode-tagline" name="tagline" placeholder="z.B. Meine Tagline"><br>';

    echo '<label for="shortcode-content">' . __('Content', 'textdomain') . ':</label>';
    echo '<textarea id="shortcode-content" name="content" placeholder="Benutzerdefinierter Inhalt"></textarea><br>';

    echo '<label for="shortcode-count">' . __('Anzahl der Einträge', 'textdomain') . ':</label>';
    echo '<input type="number" id="shortcode-count" name="count" min="1" max="50" value="6"><br>';

    echo '<label for="shortcode-id">' . __('ID', 'textdomain') . ':</label>';
    echo '<input type="text" id="shortcode-id" name="id" placeholder="z.B. my-custom-id"><br>';

    // Mehrfachauswahl für Taxonomien die angezeigt werden sollen
    echo '<label>' . __('Taxonomien anzeigen:', 'textdomain') . '</label>';
    echo '<select id="shortcode-showtaxonomies" name="showtaxonomies[]" multiple="multiple" style="min-height: 100px;">';
    foreach ($taxonomies as $taxonomy) {
        echo '<option value="' . esc_attr($taxonomy->name) . '">' . esc_html($taxonomy->label) . '</option>';
    }
    echo '</select><br>';
    echo '<small>' . __('Strg/Cmd-Taste gedrückt halten für Mehrfachauswahl', 'textdomain') . '</small><br>';

    // Zusätzliche Optionen nur für Videos (z. B. CPT "videothek")
    if ($shortcode_post_type === 'videothek') {
        // Slider Indicator
        echo '<label for="shortcode-slider-indicator">' . __('Slider Indicator', 'textdomain') . ':</label>';
        echo '<select id="shortcode-slider-indicator" name="slider_indicator">';
        echo '<option value="indicators">' . __('Slider Indicators', 'textdomain') . '</option>';
        echo '<option value="position_display">' . __('Slider Position Display', 'textdomain') . '</option>';
        echo '</select><br>';

        // Aggregation
        echo '<label for="shortcode-aggregation">' . __('Aggregation', 'textdomain') . ':</label>';
        echo '<select id="shortcode-aggregation" name="aggregation">';
        echo '<option value="false">' . __('Einzeln sliden', 'textdomain') . '</option>';
        echo '<option value="true">' . __('Alles auf einmal sliden', 'textdomain') . '</option>';
        echo '</select><br>';

        // Max Slides
        echo '<label for="shortcode-max-slides">' . __('Max Slides', 'textdomain') . ':</label>';
        echo '<input type="number" id="shortcode-max-slides" name="max_slides" min="1" max="10" value="4"><br>';

        // Gap
        echo '<label for="shortcode-gap">' . __('Gap zwischen Slides', 'textdomain') . ':</label>';
        echo '<input type="text" id="shortcode-gap" name="gap" placeholder="z.B. 20px" value="20px"><br>';

        // Hero Slider
        echo '<label for="shortcode-hero-slider">' . __('Hero Slider', 'textdomain') . ':</label>';
        echo '<select id="shortcode-hero-slider" name="hero_slider">';
        echo '<option value="false">' . __('Nein', 'textdomain') . '</option>';
        echo '<option value="true">' . __('Ja', 'textdomain') . '</option>';
        echo '</select><br>';

        // Use Arrows
        echo '<label for="shortcode-use-arrows">' . __('Pfeile anzeigen', 'textdomain') . ':</label>';
        echo '<select id="shortcode-use-arrows" name="use_arrows">';
        echo '<option value="true">' . __('Ja', 'textdomain') . '</option>';
        echo '<option value="false">' . __('Nein', 'textdomain') . '</option>';
        echo '</select><br>';

        // Checkbox für "Image only"
        echo '<label for="shortcode-image-only">' . __('Image only', 'textdomain') . ':</label>';
        echo '<select id="shortcode-image-only" name="image_only">';
        echo '<option value="true">' . __('Ja', 'textdomain') . '</option>';
        echo '<option value="false">' . __('Nein', 'textdomain') . '</option>';
        echo '</select><br>';

        // CTA Text
        echo '<label for="shortcode-cta-text">' . __('CTA Button Text', 'textdomain') . ':</label>';
        echo '<input type="text" id="shortcode-cta-text" name="cta_text" placeholder="z.B. Zu allen Videos"><br>';

        // CTA URL
        echo '<label for="shortcode-cta-url">' . __('CTA Button URL', 'textdomain') . ':</label>';
        echo '<input type="text" id="shortcode-cta-url" name="cta_url" placeholder="z.B. /mediathek-video-grids/"><br>';

        // Debug
        echo '<label for="shortcode-debug">' . __('Debug Modus', 'textdomain') . ':</label>';
        echo '<select id="shortcode-debug" name="debug">';
        echo '<option value="false">' . __('Aus', 'textdomain') . '</option>';
        echo '<option value="true">' . __('An', 'textdomain') . '</option>';
        echo '</select><br>';
    }

    echo '<button type="button" id="generate-shortcode-button">' . __('Shortcode generieren', 'textdomain') . '</button>';
    echo '</form>';

    // Platz für den generierten Shortcode
    echo '<h3>' . __('Dein generierter Shortcode:', 'textdomain') . '</h3>';
    echo '<textarea id="generated-shortcode" readonly style="width: 100%; padding: 10px;" rows="3"></textarea>';
    echo '</div>';

    // JavaScript zur dynamischen Shortcode-Generierung
    echo '<script type="text/javascript">
    document.getElementById("generate-shortcode-button").addEventListener("click", function() {
        var shortcode = "[' . $shortcode_post_type . '";
        var headline = document.getElementById("shortcode-headline").value;
        var tagline = document.getElementById("shortcode-tagline").value;
        var content = document.getElementById("shortcode-content").value;
        var count = document.getElementById("shortcode-count").value;
        var id = document.getElementById("shortcode-id").value;
        
        // Dynamisch erstellte benutzerdefinierte Taxonomien verarbeiten
        var taxonomies = document.querySelectorAll("select[name^=\'taxonomy_\']");
        taxonomies.forEach(function(select) {
            var selected = select.value;
            if (selected && selected !== "all") {
                var taxonomyName = select.name.replace("taxonomy_", "");
                shortcode += " " + taxonomyName + "=\\"" + selected + "\\"";
            }
        });

        if (headline) {
            shortcode += " headline=\\"" + headline + "\\"";
        }
        if (tagline) {
            shortcode += " tagline=\\"" + tagline + "\\"";
        }
        if (content) {
            shortcode += " content=\\"" + content + "\\"";
        }
        if (count) {
            shortcode += " count=\\"" + count + "\\"";
        }
        if (id) {
            shortcode += " id=\\"" + id + "\\"";
        }
        
        // Mehrfachauswahl für Taxonomien
        var showTaxonomiesSelect = document.getElementById("shortcode-showtaxonomies");
        var selectedTaxonomies = Array.from(showTaxonomiesSelect.selectedOptions).map(function(option) {
            return option.value;
        });
        
        if (selectedTaxonomies.length > 0) {
            shortcode += " showtaxonomies=\\"" + selectedTaxonomies.join(",") + "\\"";
        }

        // Zusätzliche Optionen für Videos
        if ("' . $shortcode_post_type . '" === "videothek") {
            // Slider Indicator
            var sliderIndicator = document.getElementById("shortcode-slider-indicator").value;
            shortcode += " slider_indicator=\\"" + sliderIndicator + "\\"";
            
            // Aggregation
            var aggregation = document.getElementById("shortcode-aggregation").value;
            shortcode += " aggregation=\\"" + aggregation + "\\"";
            
            // Max Slides
            var maxSlides = document.getElementById("shortcode-max-slides").value;
            shortcode += " max_slides=\\"" + maxSlides + "\\"";
            
            // Gap
            var gap = document.getElementById("shortcode-gap").value;
            shortcode += " gap=\\"" + gap + "\\"";
            
            // Hero Slider
            var heroSlider = document.getElementById("shortcode-hero-slider").value;
            shortcode += " hero_slider=\\"" + heroSlider + "\\"";
            
            // Use Arrows
            var useArrows = document.getElementById("shortcode-use-arrows").value;
            shortcode += " use_arrows=\\"" + useArrows + "\\"";
            
            // Image Only
            var imageOnly = document.getElementById("shortcode-image-only").value;
            shortcode += " image_only=\\"" + imageOnly + "\\"";
            
            // CTA Text und URL
            var ctaText = document.getElementById("shortcode-cta-text").value;
            var ctaUrl = document.getElementById("shortcode-cta-url").value;
            if (ctaText) {
                shortcode += " cta_text=\\"" + ctaText + "\\"";
            }
            if (ctaUrl) {
                shortcode += " cta_url=\\"" + ctaUrl + "\\"";
            }
            
            // Debug
            var debug = document.getElementById("shortcode-debug").value;
            shortcode += " debug=\\"" + debug + "\\"";
        }

        shortcode += "]";
        document.getElementById("generated-shortcode").value = shortcode;
    });
    </script>';

    echo '<style>
    .shortcode-area {
        margin-right: 20px;
        padding: 0 1rem 1rem 1rem;
        border: 1px solid #c3c4c7;
        margin-top: 2rem;
        background: #ffffff;
    }
    form#shortcode-generator-form {
        display: flex;
        flex-direction: column;
    }
    label {
        margin-top: 10px;
        font-weight: 600;
    }
    input, select, textarea {
        margin-bottom: 5px;
    }
    #generate-shortcode-button {
        margin-top: 20px;
        padding: 8px 16px;
        background-color: #2271b1;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        align-self: flex-start;
    }
    #generate-shortcode-button:hover {
        background-color: #135e96;
    }
    #generated-shortcode {
        margin-top: 10px;
        background-color: #f0f0f1;
        border: 1px solid #c3c4c7;
    }
    </style>';
}
add_action("all_admin_notices", "add_dynamic_shortcode_generator_above_post_list");


/**
 * Load Admin Settings 
 */
require_once plugin_dir_path(__FILE__) . 'admin/class-admin.php';

/**
 * Detect Page Builder function
 */
require_once plugin_dir_path(__FILE__) . 'includes/utils/detect_active_page_builder.php';
// other utils 
require_once plugin_dir_path(__FILE__) . 'includes/utils/html_pattern_to_css_selector.php';
require_once plugin_dir_path(__FILE__) . 'includes/utils/vecura_generate_random_id.php';



/**
 * Load Shordcodes 
 */
require_once plugin_dir_path(__FILE__) . 'shortcodes/social-wall-shortcode.php';
require_once plugin_dir_path(__FILE__) . 'shortcodes/news-shortcode.php';
require_once plugin_dir_path(__FILE__) . 'shortcodes/video-shortcode.php';

/**
 * Load meta boxen in pages 
 */
require_once plugin_dir_path(__FILE__) . 'includes/pages/metabox-mediathek-styles.php';


/**
 * Hide CPT "news" if news should use "posts" as source 
 */
function vecura_hide_news_cpt_in_backend() {
    $news_source = get_option('vecura_mediathek_news_source', 'cpt');

    if ($news_source === 'posts') {
        global $menu, $submenu;
        
        // Entferne "News" aus dem Admin-Menü
        remove_menu_page('edit.php?post_type=news');

        // Optional: Falls "News" auch in einem Untermenü vorkommt, könnte das nötig sein:
        foreach ($submenu as $key => $items) {
            foreach ($items as $index => $item) {
                if (strpos($item[2], 'edit.php?post_type=news') !== false) {
                    unset($submenu[$key][$index]);
                }
            }
        }
    }
}
add_action('admin_menu', 'vecura_hide_news_cpt_in_backend', 999);

// function vecura_remove_news_from_submenu() {
//     $news_source = get_option('vecura_mediathek_news_source', 'cpt');

//     if ($news_source === 'posts') {
//         global $submenu;

//         // Ersetze 'vecura-mediahub' mit dem Slug deines Hauptmenüs
//         $parent_slug = 'vecura-mediahub';

//         if (isset($submenu[$parent_slug])) {
//             foreach ($submenu[$parent_slug] as $index => $item) {
//                 if (strpos($item[2], 'edit.php?post_type=news') !== false) {
//                     unset($submenu[$parent_slug][$index]);
//                 }
//             }
//         }
//     }
// }
// add_action('admin_menu', 'vecura_remove_news_from_submenu', 999);
